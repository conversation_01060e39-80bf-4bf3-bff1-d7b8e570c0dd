{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Legacy bathroom design", "description": "The selection of products for a bathroom as used for 1.0 demo", "properties": {"id": {"type": "string"}, "faucet": {"type": "string", "format": "UUID"}, "floorTile": {"type": "string", "format": "UUID"}, "floorTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "lighting": {"type": "string", "format": "UUID"}, "mirror": {"type": "string", "format": "UUID"}, "paint": {"type": "string", "format": "UUID"}, "shelves": {"type": "string", "format": "UUID"}, "showerFloorTile": {"type": ["string", "null"], "format": "UUID"}, "showerFloorTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "showerSystem": {"type": ["string", "null"], "format": "UUID"}, "showerWallTile": {"type": ["string", "null"], "format": "UUID"}, "showerWallTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "showerShortWallTile": {"type": ["string", "null"], "format": "UUID"}, "showerGlass": {"type": ["string", "null"], "format": "UUID"}, "toilet": {"type": "string", "format": "UUID"}, "tub": {"type": ["string", "null"], "format": "UUID"}, "tubDoor": {"type": ["string", "null"], "format": "UUID"}, "tubFiller": {"type": ["string", "null"], "format": "UUID"}, "vanity": {"type": "string", "format": "UUID"}, "wallpaper": {"type": ["string", "null"], "format": "UUID"}, "wallpaperPlacement": {"enum": ["None", "AllWalls", "VanityWall", null]}, "wallTile": {"type": ["string", "null"], "format": "UUID"}, "wallTilePlacement": {"enum": ["None", "FullWall", "HalfWall", "VanityFullWall", "VanityHalfWall", null]}, "wallTilePattern": {"enum": ["Vertical", "Horizontal", "HalfOffset", "ThirdOffset", "<PERSON><PERSON><PERSON>", null]}, "nicheTile": {"type": ["string", "null"], "format": "UUID"}, "tags": {"type": "integer"}, "title": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "leadTimeDays": {"type": "integer"}, "skuCount": {"type": "integer"}, "totalPrice": {"type": "integer"}, "isShowerGlassVisible": {"type": "boolean"}, "isTubDoorVisible": {"type": "boolean"}, "isNichesVisible": {"type": "boolean"}, "lastUpdatedDateTime": {"type": "string", "format": "date-time"}}, "required": ["faucet", "floorTile", "mirror", "toilet", "vanity"], "additionalProperties": false}