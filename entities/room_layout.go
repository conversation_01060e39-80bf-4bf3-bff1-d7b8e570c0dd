package entities

import (
	"time"

	"github.com/google/uuid"
)

type ShowerEnclosureType string

const (
	NoShowerEnclosure      ShowerEnclosureType = "None"
	SlidingShowerEnclosure ShowerEnclosureType = "Sliding"
	FixedShowerEnclosure   ShowerEnclosureType = "Fixed"
)

type AlcoveTub struct {
	LayoutId uuid.UUID           `json:"layoutId"`
	DoorType ShowerEnclosureType `json:"doorType"`
	ShowerId *uuid.UUID          `json:"showerSystem,omitempty"`
}

type WetArea struct {
	LayoutId           uuid.UUID           `json:"layoutId"`
	GlassType          ShowerEnclosureType `json:"glassType"`
	ShowerIds          []uuid.UUID         `json:"showerSystems"`
	AlcoveTubs         []AlcoveTub         `json:"alcoveTubs"`
	FreestandingTubIds []uuid.UUID         `json:"freestandingTubs"`
}

type RoomLayout struct {
	Id        uuid.UUID
	UpdatedAt time.Time

	WetAreas []WetArea
	RawData  []byte
	Hash     uint64
}
