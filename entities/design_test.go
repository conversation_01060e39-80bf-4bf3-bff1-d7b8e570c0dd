package entities_test

import (
	"testing"
	"time"

	"github.com/go-json-experiment/json"
	"github.com/go-json-experiment/json/jsontext"
	"github.com/google/uuid"
	"github.com/nsf/jsondiff"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

func TestRoundTrippingJSON(t *testing.T) {
	testDesign := entities.Design{
		Id: uuid.New(),
		Metadata: entities.Metadata{
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
			Title:       "Test Design",
			Description: "This is a test design",
		},
		ProductSelections: entities.ProductSelections{
			Floors: entities.Floors{
				Default: entities.Tile{
					ProductId: uuid.New(),
					Pattern:   entities.Herringbone,
				},
			},
			Toilets: entities.Toilets{Default: uuid.New()},
			VanityAreas: entities.VanityAreas{
				Default: entities.VanityArea{
					Vanity: uuid.New(),
					Faucet: uuid.New(),
					Mirror: uuid.New(),
				},
			},
		},
	}
	testDesign.ProductSelections.WallSections.Default = entities.WallSection{Paint: &entities.Paint{uuid.New()}}
	wainscoting := entities.Wainscoting{HeightInches: 3, Schluter: entities.Schluter{"Black"}}
	vanityWall := entities.WallSection{
		Tile: &entities.WallTile{
			Main: entities.Tile{
				ProductId: uuid.New(),
				Pattern:   entities.Herringbone,
			},
		},
		Wallpaper: &entities.Wallpaper{
			ProductId: uuid.New(),
		},
		Wainscoting: &wainscoting,
	}
	if !vanityWall.IsValid() {
		t.Fatal("expected valid wall section")
	}
	testDesign.ProductSelections.WallSections.Overrides = make(map[uuid.UUID]entities.WallSection)
	testDesign.ProductSelections.WallSections.Overrides[uuid.New()] = vanityWall
	testDesign.ProductSelections.WetAreas = &entities.WetAreas{
		Default: entities.WetAreaSelections{
			FloorTiling: entities.Tile{
				ProductId: uuid.New(),
				Pattern:   entities.HalfOffset,
			},
			WallTiling: entities.WallTiling{
				Default: entities.WallTile{
					Main: entities.Tile{
						ProductId: uuid.New(),
						Pattern:   entities.VerticalStacked,
					},
				},
			},
			Enclosure: uuid.New(),
		},
	}
	testDesign.ProductSelections.ShowerSystems = &entities.ShowerSystems{
		Default: entities.ShowerSystem{
			Position:  entities.ShowerSystemPositionWall,
			ProductID: uuid.New(),
		},
	}
	testDesign.ProductSelections.Shelving = &entities.Shelving{
		Default: uuid.New(),
	}

	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	(*jsontext.Value)(&data).Indent()
	expected := entities.Design{}
	if err = json.Unmarshal(data, &expected); err != nil {
		t.Logf("\n%v", string(data))
		t.Fatal(err)
	}
	data2, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	(*jsontext.Value)(&data2).Indent()
	opts := jsondiff.DefaultConsoleOptions()
	if diff, msg := jsondiff.Compare(data, data2, &opts); diff != jsondiff.FullMatch {
		t.Errorf("mismatch after round-trip:%s", msg)
	}
}
