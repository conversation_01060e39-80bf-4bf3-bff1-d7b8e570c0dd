package entities

import (
	"time"

	"github.com/google/uuid"
)

type TilePattern string

const (
	HorizontalStacked TilePattern = "Horizontal"
	VerticalStacked   TilePattern = "Vertical"
	HalfOffset        TilePattern = "HalfOffset"
	ThirdOffset       TilePattern = "ThirdOffset"
	Herringbone       TilePattern = "Herringbone"
)

type DesignStatus string

const (
	Preview        DesignStatus = "Preview"
	FaveDesign     DesignStatus = "Fave"
	ArchivedDesign DesignStatus = "Archived"
)

type ColorScheme string

const (
	Neutral ColorScheme = "Neutral"
	Bold    ColorScheme = "Bold"
)

type Style string

const (
	Traditional  Style = "Traditional"
	Transitional Style = "Transitional"
	MidCentury   Style = "Mid-century"
	Modern       Style = "Modern"
)

type ProductRef struct {
	Identifier uuid.UUID `json:"identifier"`
	ProductId  uuid.UUID `json:"productId"`
}

// ffjson: skip
type DefaultWithOverrides[T any] struct {
	Default   T               `json:"default"`
	Overrides map[uuid.UUID]T `json:"overrides"`
}

type Toilets DefaultWithOverrides[uuid.UUID]

type Tile struct {
	ProductId uuid.UUID   `json:"productId"`
	Pattern   TilePattern `json:"pattern"`
}

type Floors DefaultWithOverrides[Tile]

type WallTile struct {
	Main  Tile  `json:"main"`
	Niche *Tile `json:"niche,omitempty"`
}

type Paint struct {
	ProductId uuid.UUID `json:"productId"`
}

type Wallpaper struct {
	ProductId uuid.UUID `json:"productId"`
}

type Schluter struct {
	Color string `json:"color"` // TODO: consider enum.
}
type Wainscoting struct {
	HeightInches uint     `json:"heightInches"`
	Schluter     Schluter `json:"schluter"`
}

type WallSection struct {
	Paint       *Paint       `json:"paint,omitempty"`
	Wallpaper   *Wallpaper   `json:"wallpaper,omitempty"`
	Tile        *WallTile    `json:"tile,omitempty"`
	Wainscoting *Wainscoting `json:"wainscoting,omitempty"`
}

func (w *WallSection) IsValid() bool {
	if w.Paint == nil && w.Wallpaper == nil && w.Tile == nil {
		return false
	} else if w.Paint != nil && w.Wallpaper != nil {
		return false
	} else if w.Paint != nil && w.Tile != nil && w.Wainscoting == nil {
		return false
	} else if w.Wallpaper != nil && w.Tile != nil && w.Wainscoting == nil {
		return false
	} else if w.Tile == nil && w.Wainscoting != nil {
		return false
	} else if w.Paint == nil && w.Wallpaper == nil && w.Wainscoting != nil {
		return false
	}
	return true
}

type WallSections DefaultWithOverrides[WallSection]

type LightingPosition string

const (
	Top  LightingPosition = "Top"
	Side LightingPosition = "Side"
)

type Lighting struct {
	ProductId uuid.UUID        `json:"productId"`
	Position  LightingPosition `json:"position"`
}

type VanityArea struct {
	Vanity   uuid.UUID `json:"vanity"`
	Faucet   uuid.UUID `json:"faucet"`
	Mirror   uuid.UUID `json:"mirror"`
	Lighting *Lighting `json:"lighting,omitempty"`
}

type VanityAreas DefaultWithOverrides[VanityArea]

type TubArea struct {
	Tub    ProductRef  `json:"tub"`
	Filler *ProductRef `json:"filler,omitempty"`
}
type TubAreas DefaultWithOverrides[TubArea]

type ShowerSystemPosition string

const (
	ShowerSystemPositionWall    ShowerSystemPosition = "Wall"
	ShowerSystemPositionCeiling ShowerSystemPosition = "Ceiling"
)

type ShowerSystem struct {
	Position  ShowerSystemPosition `json:"position"`
	ProductID uuid.UUID            `json:"productId"`
}
type ShowerSystems DefaultWithOverrides[ShowerSystem]

type WallTiling DefaultWithOverrides[WallTile]
type WetAreaSelections struct {
	FloorTiling Tile       `json:"floorTiling"`
	WallTiling  WallTiling `json:"wallTiling"`
	Enclosure   uuid.UUID  `json:"enclosure"`
	CurbTiling  *Tile      `json:"curbTiling,omitempty"`
}
type WetAreas DefaultWithOverrides[WetAreaSelections]

type Shelving DefaultWithOverrides[uuid.UUID]

type ProductSelections struct {
	Floors        Floors         `json:"floors"`
	WallSections  WallSections   `json:"wallSections"`
	Toilets       Toilets        `json:"toilets"`
	VanityAreas   VanityAreas    `json:"vanityAreas"`
	WetAreas      *WetAreas      `json:"wetAreas,omitempty"`
	ShowerSystems *ShowerSystems `json:"showerSystems,omitempty"`
	TubAreas      *TubAreas      `json:"tubAreas,omitempty"`
	Shelving      *Shelving      `json:"shelving,omitempty"`
}

type Metadata struct {
	CreatedAt   time.Time     `json:"createdAt"`
	UpdatedAt   time.Time     `json:"updatedAt"`
	Title       string        `json:"title"`
	Description string        `json:"description"`
	Status      *DesignStatus `json:"status,omitempty"`
	ColorScheme *ColorScheme  `json:"colorScheme,omitempty"`
	Style       *Style        `json:"style,omitempty"`
}

type RenderPrefs struct {
	IsShowerGlassVisible *bool `json:"isShowerGlassVisible,omitempty"`
	IsTubDoorVisible     *bool `json:"isTubDoorVisible,omitempty"`
	AreNichesVisible     *bool `json:"areNichesVisible,omitempty"`
}

type Design struct {
	Id        uuid.UUID `json:"id"`
	ProjectId string    `json:"projectId"`

	Metadata          Metadata          `json:"metadata"`
	ProductSelections ProductSelections `json:"productSelections"`
	RenderPrefs       *RenderPrefs      `json:"renderPrefs,omitempty"`
}
