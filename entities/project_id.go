package entities

import "fmt"

type ProjectId string

// NewProjectId creates a new ProjectId from a string
func NewProjectId(id string) ProjectId {
	return ProjectId(id)
}

// String returns the string representation of the ProjectId
func (p ProjectId) String() string {
	return string(p)
}

// IsValid checks if the ProjectId is valid
func (p ProjectId) IsValid() bool {
	// ProjectId should not be empty
	return p != ""
}

// MarshalJSON implements the json.Marshaler interface
func (p ProjectId) MarshalJSON() ([]byte, error) {
	return []byte(`"` + string(p) + `"`), nil
}

// UnmarshalJSON implements the json.Unmarshaler interface
func (p *ProjectId) UnmarshalJSON(data []byte) error {
	// Remove quotes
	if len(data) >= 2 && data[0] == '"' && data[len(data)-1] == '"' {
		*p = ProjectId(data[1 : len(data)-1])
		return nil
	}
	return fmt.Errorf("invalid ProjectId format")
}
