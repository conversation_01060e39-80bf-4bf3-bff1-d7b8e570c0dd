# Design server

A Go server offering a REST API for room designs that adhere to a JSON Schema.
This replaces the prototype monolith as the canonical home for room designs.

The architecture is based on the Clean Architecture with separation of responsibilities
between commands that mutate state and queries that retrieve it.

It currently uses the monolith's database for persistence but will soon migrate
to a new Postgres database.

## Getting started

Initialize repo after cloning: `go mod tidy`

Run all tests: `go test --tags=integration ./...`

## Run the server

Set the `PGPASSWORD` env var for the monolith DB, then run the following command:

```sh
go run .
```

Try it by opening a URL like this in a browser:
<http://localhost:8080/projects/DEFAULT/designs>

## Change the Postgres schema

The schema is managed by [dbmate](https://github.com/amacneil/dbmate).
You can see the current schema `frameworks/db/schema.sql`.
The settings are stored in the `.env` file.
To create a new migration, run the following command:

```sh
dbmate new <description>
```

This will create a new file in the `frameworks/db` directory.
Edit the file to add the SQL statements for the migration.
Then run `dbmate up` to apply the migration:
