package adapters

import (
	"encoding/binary"
	"encoding/json"
	"time"

	"github.com/cespare/xxhash/v2"
	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type RoomLayout struct {
	Id        uuid.UUID `json:"id"`
	UpdatedAt time.Time `json:"updatedAt"`

	WetAreas []entities.WetArea `json:"wetAreas"`
	RawData  *json.RawMessage   `json:"rawData"`
}

func (rl *RoomLayout) ToEntity() entities.RoomLayout {
	roomLayoutHash := xxhash.Sum64(*rl.RawData)
	entity := entities.RoomLayout{
		Id:        rl.Id,
		UpdatedAt: rl.UpdatedAt,
		WetAreas:  rl.WetAreas,
		Hash:      roomLayoutHash,
	}
	if rl.RawData != nil {
		entity.RawData = []byte(*rl.RawData)
	}
	return entity
}

func Uint64ToBytes(i uint64) []byte {
	var b [8]byte
	binary.LittleEndian.PutUint64(b[:], i)
	return b[:]
}
