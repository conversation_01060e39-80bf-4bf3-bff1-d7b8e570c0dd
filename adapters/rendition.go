package adapters

import (
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type Rendition struct {
	Id        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	Status entities.RenditionStatus `json:"status"`
	URL    string                   `json:"url"`
}

func FromDomainRendition(r entities.Rendition) Rendition {
	return Rendition{
		Id:        r.Id,
		CreatedAt: r.Created<PERSON>t,
		UpdatedAt: r.UpdatedAt,
		Status:    r.Status,
		URL:       r.URL.String(),
	}
}
