package gateways

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"

	"github.com/invopop/jsonschema"
	"github.com/openai/openai-go" // imported as openai

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const SYSTEM_INSTRUCTIONS_DESIGN = `You are a senior virtual interior design assistant with many years of experience. 
You are picking a set of products from within a catalog and will provide them a design style summary at the end based on the overall style of the selected products.`
const PROMPT_PREFIX_DESIGN = `
Step 1 - Review the description for each product and create a summary of the style of the products, including plumbing finish material, vanity type, vanity mounting type, mood, colors, color palette and atmosphere.
Step 2 - Based on the style summary create a 1-3 word Design Style title describing for their bathroom design and a 200-250 character Design Style Description that references elements in the bathroom like colors present, finishes and element mounting types.

For context, you are an experienced bathroom designer. You are helping a novice homeowner select materials for their bathroom remodel. 
This homeowner does not have a ton of design or construction knowledge so it's important to speak in language they undersand and mention features the general public would care about. 
This content is going to go in a software application to help people visualize and buy materials for their remodeling project! 
You want all the content here to be as personalized as possible, so it gives them confidence in their ability to transact.
`

type OpenAI struct {
	client openai.Client
}

func NewOpenAI(client openai.Client) *OpenAI {
	return &OpenAI{client: client}
}

type TitleAndDescription struct {
	Title       string `json:"title"`
	Description string `json:"description"`
}

func GenerateSchema[T any]() interface{} {
	reflector := jsonschema.Reflector{
		AllowAdditionalProperties: false,
		DoNotReference:            true,
	}
	var v T
	schema := reflector.Reflect(v)
	return schema
}

// Generate the JSON schema at initialization time
func (o *OpenAI) GenerateDesignTitleAndDescription(ctx context.Context, design usecases.Design) (string, string, error) {
	productDescriptions, err := buildProductDescriptions(design)
	if err != nil {
		return "", "", err
	}
	TitleAndDescriptionSchema := GenerateSchema[TitleAndDescription]()
	schemaParam := openai.ResponseFormatJSONSchemaJSONSchemaParam{
		Name:        "title_and_description",
		Description: openai.String("The title and description of the design"),
		Schema:      TitleAndDescriptionSchema,
		Strict:      openai.Bool(true),
	}
	chatCompletion, err := o.client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: []openai.ChatCompletionMessageParamUnion{
			openai.SystemMessage(SYSTEM_INSTRUCTIONS_DESIGN),
			openai.UserMessage(buildDesignPrompt(PROMPT_PREFIX_DESIGN, productDescriptions)),
		},
		Model: openai.ChatModelO3Mini,
		ResponseFormat: openai.ChatCompletionNewParamsResponseFormatUnion{
			OfJSONSchema: &openai.ResponseFormatJSONSchemaParam{
				JSONSchema: schemaParam,
			},
		},
	})
	if err != nil {
		return "", "", err
	}
	if len(chatCompletion.Choices) == 0 {
		return "", "", fmt.Errorf("no choices returned")
	}
	titleAndDescription := chatCompletion.Choices[0].Message.Content
	var result struct {
		Title       string `json:"title"`
		Description string `json:"description"`
	}
	if err := json.Unmarshal([]byte(titleAndDescription), &result); err != nil {
		return "", "", fmt.Errorf("failed to unmarshal result: %w", err)
	}
	return result.Title, result.Description, nil
}

// buildProductDescriptions constructs product descriptions via Catalog.
func buildProductDescriptions(design usecases.Design) (map[string]string, error) {
	idToProduct := make(map[string]string)
	var productIds []string

	if design.FloorTile != nil {
		idToProduct[design.FloorTile.String()] = "floor tile"
	}
	if design.Toilet != nil {
		idToProduct[design.Toilet.String()] = "toilet"
	}
	if design.Vanity != nil {
		idToProduct[design.Vanity.String()] = "vanity"
	}
	if design.Faucet != nil {
		idToProduct[design.Faucet.String()] = "faucet"
	}
	if design.Mirror != nil {
		idToProduct[design.Mirror.String()] = "mirror"
	}
	if design.Paint != nil {
		idToProduct[design.Paint.String()] = "paint"
	}
	if design.WallTile != nil {
		idToProduct[design.WallTile.String()] = "wall tile"
	}
	if design.ShowerWallTile != nil {
		idToProduct[design.ShowerWallTile.String()] = "shower wall tile"
	}
	if design.ShowerFloorTile != nil {
		idToProduct[design.ShowerFloorTile.String()] = "shower floor tile"
	}
	if design.ShowerSystem != nil {
		idToProduct[design.ShowerSystem.String()] = "shower system"
	}
	if design.ShowerShortWallTile != nil {
		idToProduct[design.ShowerShortWallTile.String()] = "shower short wall tile"
	}
	if design.ShowerGlass != nil {
		idToProduct[design.ShowerGlass.String()] = "shower glass"
	}
	if design.Tub != nil {
		idToProduct[design.Tub.String()] = "tub"
	}
	if design.TubDoor != nil {
		idToProduct[design.TubDoor.String()] = "tub door"
	}
	if design.NicheTile != nil {
		idToProduct[design.NicheTile.String()] = "niche tile"
	}
	if design.Wallpaper != nil {
		idToProduct[design.Wallpaper.String()] = "wallpaper"
	}
	if design.Shelving != nil {
		idToProduct[design.Shelving.String()] = "shelving"
	}
	if design.Lighting != nil {
		idToProduct[design.Lighting.String()] = "lighting"
	}

	if len(idToProduct) == 0 {
		log.Println("No product IDs found in design!")
		return make(map[string]string), nil
	}
	for id := range idToProduct {
		productIds = append(productIds, id)
	}

	url := fmt.Sprintf("https://api.averyapi.com/catalog/v2/products/renderable-products/%s?include[]=details", strings.Join(productIds, ";"))
	resp, err := http.Get(url)
	if err != nil {
		log.Printf("Failed to get product data from catalog via URL: %v", url)
		return nil, fmt.Errorf("failed to get product data from catalog: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Printf("Failed to get product data from catalog via URL: %v", url)
		return nil, fmt.Errorf("non-200 response from catalog: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to get product data from catalog via URL: %v", url)
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var productData struct {
		Data []struct {
			ID      string `json:"id"`
			Details struct {
				Description string `json:"description"`
			} `json:"details"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &productData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal product data: %w", err)
	}

	productDescriptions := make(map[string]string)
	for _, product := range productData.Data {
		productName, ok := idToProduct[product.ID]
		if ok {
			productDescriptions[productName] = product.Details.Description
		}
	}

	return productDescriptions, nil
}

// buildDesignPrompt constructs the prompt for the design based on product descriptions.
func buildDesignPrompt(promptPrefix string, productDescriptions map[string]string) string {
	var descriptions []string
	for product, description := range productDescriptions {
		descriptions = append(descriptions, fmt.Sprintf("%s description: %s", product, description))
	}
	return fmt.Sprintf("%s\n%s", promptPrefix, strings.Join(descriptions, "\n"))
}
