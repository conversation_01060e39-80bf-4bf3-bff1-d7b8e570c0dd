//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"log"
	"os"
	"testing"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const TEST_DATABASE_URL = "postgres://postgres@localhost:5432/projects"

var testPool *pgxpool.Pool // Global pool for all tests in this package

// TestMain is the entry point for tests in this package. It sets up the
// database connection pool and closes it after all tests have run.
func TestMain(m *testing.M) {
	databaseUrl := os.Getenv("TEST_DATABASE_URL")
	if databaseUrl == "" {
		log.Println("TEST_DATABASE_URL env var not set; using default value: ", TEST_DATABASE_URL)
		databaseUrl = TEST_DATABASE_URL
	}

	var err error
	testPool, err = pgxpool.New(context.Background(), databaseUrl)
	if err != nil {
		log.Fatalf("Failed to connect to test database: %v", err)
	}
	defer testPool.Close()

	// Run all tests in the package
	exitCode := m.Run()
	os.Exit(exitCode)
}

// TestRelationalDb_Integration_Designs_CRUD performs a full create, read, update, and delete
// cycle against a live database to ensure the entire lifecycle works as expected.
func TestRelationalDb_Integration_Designs_CRUD(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	// CASCADE is crucial to also truncate tables with foreign keys.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	testDesign := usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Integration Test", Valid: true},
			Description:      sql.NullString{Valid: false},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile:      ptr(uuid.New()),
				Lighting:       ptr(uuid.New()),
				Mirror:         ptr(uuid.New()),
				Paint:          ptr(uuid.New()),
				ShowerWallTile: &uuid.Nil,
				Toilet:         ptr(uuid.New()),
			},
			Faucet:      ptr(uuid.New()),
			ShowerGlass: &uuid.Nil,
			TubDoor:     &uuid.Nil,
			Vanity:      ptr(uuid.New()),
		},
	}
	*testDesign.ShowerWallTile = uuid.New()
	*testDesign.ShowerGlass = uuid.New()
	*testDesign.TubDoor = uuid.New()

	// --- 1. Test Create ---
	createdID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Create failed")
	require.NotEqual(t, uuid.Nil, createdID, "Create should return a valid UUID")

	// --- 2. Test Read ---
	readDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after create")
	require.NotNil(t, readDesign)
	require.NotNil(t, readDesign.Created)
	require.NotEmpty(t, readDesign.ID)

	assert.Equal(t, createdID, readDesign.ID)
	assert.Equal(t, testDesign.ProjectID, readDesign.ProjectID)
	assert.Equal(t, testDesign.Title.String, readDesign.Title.String)
	assert.Equal(t, usecases.Herringbone, *readDesign.FloorTilePattern)
	assert.Equal(t, usecases.Modern, *readDesign.Style)
	assert.Equal(t, usecases.Neutral, *readDesign.ColorScheme)
	assert.Equal(t, usecases.Preview, readDesign.Status)
	assert.Equal(t, readDesign.Created, readDesign.LastUpdated)

	// --- 3. Test Update ---
	readDesign.Title = sql.NullString{String: "Updated Title", Valid: true}
	readDesign.FloorTilePattern = ptr(usecases.VerticalStacked)
	readDesign.Style = ptr(usecases.Traditional)
	readDesign.ColorScheme = ptr(usecases.Bold)
	readDesign.Status = usecases.Archived
	readDesign.TubDoorVisible = true
	readDesign.NichesVisible = true

	_, err = db.UpsertDesign(ctx, readDesign)
	require.NoError(t, err, "Update failed")

	// --- 4. Read again to verify update ---
	updatedDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after update")
	require.NotNil(t, updatedDesign)

	assert.Equal(t, "Updated Title", updatedDesign.Title.String)
	assert.Equal(t, usecases.VerticalStacked, *updatedDesign.FloorTilePattern)
	assert.Equal(t, usecases.Traditional, *updatedDesign.Style)
	assert.Equal(t, usecases.Bold, *updatedDesign.ColorScheme)
	assert.Equal(t, usecases.Archived, updatedDesign.Status)
	assert.True(t, updatedDesign.TubDoorVisible)
	assert.True(t, updatedDesign.NichesVisible)
	assert.Greater(t, updatedDesign.LastUpdated, updatedDesign.Created)
	assert.Greater(t, updatedDesign.LastUpdated, readDesign.LastUpdated)

	// --- 5. Test DesignsForProject ---
	// Create another design for the same project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Fave,
		WallpaperPlacement: usecases.AllWalls,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			FloorTilePattern: ptr(usecases.ThirdOffset),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			Vanity: ptr(uuid.New()),
			Faucet: ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for second design")

	// Fetch both designs for the original project
	designs, err := db.DesignsForProject(ctx, "PRJ-TEST123")
	require.NoError(t, err, "Fetch designs failed")
	assert.Len(t, designs, 2, "Expected 2 designs for the project")

	// --- 6. Test DesignsByProject ---
	// Create another designs for a different project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST456",
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.FullWall,
		DesignOptions: usecases.DesignOptions{
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			FloorTilePattern: ptr(usecases.VerticalStacked),
			Vanity:           ptr(uuid.New()),
			Faucet:           ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for third design")

	// Fetch all designs for both projects
	projectDesigns, errors, err := db.DesignsByProject(ctx, []entities.ProjectId{"PRJ-TEST123", "PRJ-TEST456"})
	require.NoError(t, err, "Fetch designs by project failed")
	assert.Len(t, errors, 0, "Expected no errors")
	assert.Len(t, projectDesigns, 2, "Expected designs for 2 projects")

	// --- 7. Test Delete ---
	err = db.DeleteDesign(ctx, createdID)
	require.NoError(t, err, "Delete failed")

	// --- 8. Read again to verify delete ---
	_, err = db.ReadDesign(ctx, createdID)
	require.Error(t, err, "Read should fail for a deleted design")
	assert.Contains(t, err.Error(), "not found")
}

// TestRelationalDb_Integration_InsertPreset tests the InsertPreset method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertPreset(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE template.templates CASCADE;")
	require.NoError(t, err, "Failed to truncate templates table before test")
	_, err = testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate room_designs table before test")

	// First create a template that the preset can reference
	testTemplate := usecases.Template{
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Name:            "Test Template for Preset",
		Description:     "A test template for preset testing",
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm"},
		ColorPalette:    []string{"white"},
		MaterialPalette: []string{"marble"},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: ptr(uuid.New()),
			Lighting:  ptr(uuid.New()),
			Mirror:    ptr(uuid.New()),
			Paint:     ptr(uuid.New()),
			Shelving:  ptr(uuid.New()),
			Toilet:    ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		ShowerFloorTile:    ptr(uuid.New()),
		ShowerWallTile:     ptr(uuid.New()),
		TubFiller:          ptr(uuid.New()),
		WallTilePlacement:  usecases.HalfWall,
		WallTile:           ptr(uuid.New()),
		WallpaperPlacement: usecases.NoWallpaper,
	}

	templateID, err := db.InsertTemplate(ctx, testTemplate)
	require.NoError(t, err, "Failed to create test template")

	testPreset := usecases.Preset{
		Id:         "42",
		TemplateId: templateID,
		Design: usecases.Design{
			ProjectID:          "PRJ-PRESET-TEST",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Test Preset Design", Valid: true},
				Description:      sql.NullString{String: "A test preset design", Valid: true},
				ColorScheme:      ptr(usecases.Neutral),
				Style:            ptr(usecases.Modern),
				FloorTilePattern: ptr(usecases.Herringbone),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet: ptr(uuid.New()),
				Vanity: ptr(uuid.New()),
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     false,
			NichesVisible:      true,
		},
		Rendition: entities.Rendition{
			Status: entities.RenditionPending,
		},
	}

	// Act - Insert the preset
	err = db.InsertPreset(ctx, testPreset)
	require.NoError(t, err, "InsertPreset failed")

	// Assert - Read the preset back to verify it was created correctly
	readPreset, err := db.FindPresetByLegacyId(ctx, testPreset.Id)
	require.NoError(t, err, "ReadPreset failed after insert")
	require.NotNil(t, readPreset)

	// Verify preset fields
	assert.Equal(t, testPreset.Id, readPreset.Id)
	assert.NotZero(t, readPreset.CreatedAt)
	assert.NotZero(t, readPreset.UpdatedAt)

	// Verify design fields
	assert.Equal(t, testPreset.Design.ProjectID, readPreset.Design.ProjectID)
	assert.Equal(t, testPreset.Design.Status, readPreset.Design.Status)
	assert.Equal(t, testPreset.Design.Title.String, readPreset.Design.Title.String)
	assert.Equal(t, testPreset.Design.Description.String, readPreset.Design.Description.String)
	assert.Equal(t, *testPreset.Design.ColorScheme, *readPreset.Design.ColorScheme)
	assert.Equal(t, *testPreset.Design.Style, *readPreset.Design.Style)
	assert.Equal(t, testPreset.Design.ShowerGlassVisible, readPreset.Design.ShowerGlassVisible)
	assert.Equal(t, testPreset.Design.TubDoorVisible, readPreset.Design.TubDoorVisible)
	assert.Equal(t, testPreset.Design.NichesVisible, readPreset.Design.NichesVisible)

	// Verify rendition fields
	assert.NotEqual(t, uuid.Nil, readPreset.Rendition.Id)
	assert.Equal(t, testPreset.Rendition.Status, readPreset.Rendition.Status)
}

// TestRelationalDb_Integration_InsertTemplate tests the InsertTemplate method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertTemplate(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE template.templates CASCADE;")
	require.NoError(t, err, "Failed to truncate templates table before test")

	testTemplate := usecases.Template{
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Name:            "Test Template",
		Description:     "A test template for integration testing",
		Inspiration:     "Modern minimalist design",
		Atmosphere:      []string{"calm", "serene", "modern"},
		ColorPalette:    []string{"white", "gray", "black"},
		MaterialPalette: []string{"marble", "wood", "metal"},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: ptr(uuid.New()),
			Lighting:  ptr(uuid.New()),
			Mirror:    ptr(uuid.New()),
			Paint:     ptr(uuid.New()),
			Shelving:  ptr(uuid.New()),
			Toilet:    ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		TemplateProvenance: usecases.TemplateProvenance{
			LightingBrand: ptr("Test Lighting Brand"),
			PlumbingBrand: ptr("Test Plumbing Brand"),
			ToiletBrand:   ptr("Test Toilet Brand"),
			VanityBrand:   ptr("Test Vanity Brand"),
			VanityStorage: ptr("Test Storage"),
		},
		ShowerFloorTile:    ptr(uuid.New()),
		ShowerWallTile:     ptr(uuid.New()),
		TubFiller:          ptr(uuid.New()),
		WallTilePlacement:  usecases.HalfWall,
		WallTile:           ptr(uuid.New()),
		WallpaperPlacement: usecases.VanityWall,
		Wallpaper:          ptr(uuid.New()),
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	// Act - Insert the template
	templateID, err := db.InsertTemplate(ctx, testTemplate)
	require.NoError(t, err, "InsertTemplate failed")
	require.NotEqual(t, uuid.Nil, templateID, "InsertTemplate should return a valid UUID")

	// Assert - Read the template back to verify it was created correctly
	readTemplate, err := db.ReadTemplate(ctx, templateID)
	require.NoError(t, err, "ReadTemplate failed after insert")

	// Verify template fields
	assert.Equal(t, templateID, readTemplate.ID)
	assert.Equal(t, testTemplate.ColorScheme, readTemplate.ColorScheme)
	assert.Equal(t, testTemplate.Style, readTemplate.Style)
	assert.Equal(t, testTemplate.Name, readTemplate.Name)
	assert.Equal(t, testTemplate.Description, readTemplate.Description)
	assert.Equal(t, testTemplate.Inspiration, readTemplate.Inspiration)
	assert.Equal(t, testTemplate.Atmosphere, readTemplate.Atmosphere)
	assert.Equal(t, testTemplate.ColorPalette, readTemplate.ColorPalette)
	assert.Equal(t, testTemplate.MaterialPalette, readTemplate.MaterialPalette)
	assert.NotZero(t, readTemplate.UpdatedAt)

	// Verify fixed product selections
	assert.Equal(t, testTemplate.FixedProductSelections.FloorTile, readTemplate.FixedProductSelections.FloorTile)
	assert.Equal(t, testTemplate.FixedProductSelections.Lighting, readTemplate.FixedProductSelections.Lighting)
	assert.Equal(t, testTemplate.FixedProductSelections.Mirror, readTemplate.FixedProductSelections.Mirror)
	assert.Equal(t, testTemplate.FixedProductSelections.Paint, readTemplate.FixedProductSelections.Paint)
	assert.Equal(t, testTemplate.FixedProductSelections.Shelving, readTemplate.FixedProductSelections.Shelving)
	assert.Equal(t, testTemplate.FixedProductSelections.Toilet, readTemplate.FixedProductSelections.Toilet)

	// Verify product selection options
	assert.Equal(t, testTemplate.ProductSelectionOptions.AlcoveTub, readTemplate.ProductSelectionOptions.AlcoveTub)
	assert.Equal(t, testTemplate.ProductSelectionOptions.FreestandingTub, readTemplate.ProductSelectionOptions.FreestandingTub)
	assert.Equal(t, testTemplate.ProductSelectionOptions.ShowerGlassFixed, readTemplate.ProductSelectionOptions.ShowerGlassFixed)
	assert.Equal(t, testTemplate.ProductSelectionOptions.ShowerGlassSliding, readTemplate.ProductSelectionOptions.ShowerGlassSliding)
	assert.Equal(t, testTemplate.ProductSelectionOptions.ShowerSystemCombo, readTemplate.ProductSelectionOptions.ShowerSystemCombo)
	assert.Equal(t, testTemplate.ProductSelectionOptions.ShowerSystemSolo, readTemplate.ProductSelectionOptions.ShowerSystemSolo)
	assert.Equal(t, testTemplate.ProductSelectionOptions.TubDoorFixed, readTemplate.ProductSelectionOptions.TubDoorFixed)
	assert.Equal(t, testTemplate.ProductSelectionOptions.TubDoorSliding, readTemplate.ProductSelectionOptions.TubDoorSliding)

	// Verify template provenance
	assert.Equal(t, testTemplate.TemplateProvenance.LightingBrand, readTemplate.TemplateProvenance.LightingBrand)
	assert.Equal(t, testTemplate.TemplateProvenance.PlumbingBrand, readTemplate.TemplateProvenance.PlumbingBrand)
	assert.Equal(t, testTemplate.TemplateProvenance.ToiletBrand, readTemplate.TemplateProvenance.ToiletBrand)
	assert.Equal(t, testTemplate.TemplateProvenance.VanityBrand, readTemplate.TemplateProvenance.VanityBrand)
	assert.Equal(t, testTemplate.TemplateProvenance.VanityStorage, readTemplate.TemplateProvenance.VanityStorage)

	// Verify other template product selections
	assert.Equal(t, testTemplate.ShowerFloorTile, readTemplate.ShowerFloorTile)
	assert.Equal(t, testTemplate.ShowerWallTile, readTemplate.ShowerWallTile)
	assert.Equal(t, testTemplate.TubFiller, readTemplate.TubFiller)
	assert.Equal(t, testTemplate.WallTilePlacement, readTemplate.WallTilePlacement)
	assert.Equal(t, testTemplate.WallTile, readTemplate.WallTile)
	assert.Equal(t, testTemplate.WallpaperPlacement, readTemplate.WallpaperPlacement)
	assert.Equal(t, testTemplate.Wallpaper, readTemplate.Wallpaper)

	// Verify vanity scaling options
	assert.Equal(t, len(testTemplate.VanityScalingOptions), len(readTemplate.VanityScalingOptions))
	for size, expectedOption := range testTemplate.VanityScalingOptions {
		actualOption, exists := readTemplate.VanityScalingOptions[size]
		assert.True(t, exists, "Expected vanity scaling option for size %d", size)
		assert.Equal(t, expectedOption.VanityProductID, actualOption.VanityProductID)
		assert.Equal(t, expectedOption.FaucetProductID, actualOption.FaucetProductID)
	}
}

// Helper function to get a pointer to a value
func ptr[T any](v T) *T {
	return &v
}
