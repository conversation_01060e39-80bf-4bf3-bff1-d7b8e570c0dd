Enum color_scheme_enum {
  Neutral
  Bold
}

Enum style_enum {
  Modern
  Traditional
  Transitional
}

Enum rendition_status_enum {
  Pending
  Started
  Completed
  Archived
  Outdated
}

Enum tile_pattern_enum {
  Horizontal
  Vertical
  HalfOffset
  ThirdOffset
  Herringbone
}

Enum shower_head_position_enum {
  Wall
  Ceiling
}

Enum lighting_position_enum {
  Top
  Side
}

Table product_categories {
  id uuid [pk]
  name text
}

Table colors {
  id uuid [pk]
  name text
}

Table room_designs {
  id uuid [pk, default: `gen_random_uuid()`]
  project_id text [not null]
  created timestamptz [not null, default: `now()`]
  updated timestamptz [not null, default: `now()`]
  status text
  title text
  description text
  color_scheme color_scheme_enum
  style style_enum
}

Table renditions {
  id uuid [pk]
  room_design_id uuid [not null, ref: > room_designs.id]
  created timestamptz [not null, default: `now()`]
  updated timestamptz [not null, default: `now()`]
  status rendition_status_enum [not null]
  url text
  Note: '''PostgreSQL CHECK constraint:
  CONSTRAINT chk_rendition_url CHECK (status <> 'completed' OR url IS NOT NULL)'''
}

Table product_selections {
  id bigint [pk, increment]
  room_design_id uuid [not null, ref: > room_designs.id]
  category_id uuid [not null, ref: > product_categories.id]
  layout_id uuid [note: 'Reference to a specific instance of an item in the room layout.']
  product_id uuid [not null, note: 'Soft ref. to product in Catalog.']

  indexes {
    (room_design_id, category_id, layout_id) [unique, note: 'Postgres UNIQUE NULLS NOT DISTINCT']
  }

}

Table floor_tile {
  room_design_id uuid [not null, ref: > room_designs.id]
  layout_id uuid [note: 'Optional (soft) reference to a specific floor in the room layout.']
  product_id uuid [not null, note: 'Soft ref. to tile in Catalog.']
  pattern tile_pattern_enum [not null]

  indexes {
    (room_design_id, layout_id) [unique, note: 'Postgres UNIQUE NULLS NOT DISTINCT']
  }

}

Table shower_systems {
  room_design_id uuid [not null, ref: > room_designs.id]
  layout_id uuid [note: 'Optional (soft) reference to a specific floor in the room layout.']
  product_id uuid [not null, note: 'Soft ref. to tile in Catalog.']
  position shower_head_position_enum [not null]

  indexes {
    (room_design_id, layout_id) [unique, note: 'Postgres UNIQUE NULLS NOT DISTINCT']
  }

}

Table tubs {
  room_design_id uuid [pk, not null, ref: > room_designs.id]
  layout_id uuid [pk, not null, note: 'Reference to a specific tub instance in the layout.']
  product_id uuid [not null, note: 'The tub product itself.']

  filler_layout_id uuid [note: 'Identifier for the tub filler instance associated with this specific tub.']
  filler_product_id uuid [note: 'Product for the tub filler associated with this specific tub.']

  Note: '''PostgreSQL CHECK constraint ensures filler details are consistent:
  CONSTRAINT chk_tub_filler CHECK (
  (filler_layout_id IS NULL AND filler_product_id IS NULL) OR
  (filler_layout_id IS NOT NULL AND filler_product_id IS NOT NULL)
  )'''
}

Table vanity_areas {
  room_design_id uuid [not null, ref: > room_designs.id]
  layout_id uuid [note: 'NULL indicates the \'default\' entry.']

  vanity_product_id uuid [not null]
  faucet_product_id uuid [not null]
  mirror_product_id uuid [not null]
  lighting_product_id uuid
  lighting_position lighting_position_enum

  indexes {
    (room_design_id, layout_id) [unique]
  }

  Note: '''PostgreSQL CHECK constraint ensures lighting details are consistent:
  CHECK ((lighting_product_id IS NULL AND lighting_position IS NULL) OR (lighting_product_id IS NOT NULL AND lighting_position IS NOT NULL))'''
}

Table wall_sections {
  room_design_id uuid [not null, ref: > room_designs.id]
  layout_id uuid [note: 'NULL indicates the \'default\' entry.']

  paint uuid
  wallpaper uuid
  tile uuid
  tile_pattern tile_pattern_enum [note: 'Only relevant if section_type = \'tiling\'.']
  wainscoting_id bigint [pk, ref: > wainscoting.id]

  indexes {
    (room_design_id, layout_id) [unique, note: 'Postgres UNIQUE NULLS NOT DISTINCT']
  }

  Note: '''PostgreSQL CHECK constraint (simplified interpretation):
  CHECK ((tile IS NULL) = (tile_pattern IS NULL)),
  CHECK ((wainscoting_id IS NOT NULL AND tile IS NOT NULL AND num_nonnulls(paint, wallpaper) = 1)
     OR  (wainscoting_id IS NULL AND num_nonnulls(paint, wallpaper, tile) = 1))
  '''
}

Table wainscoting {
  id bigint [pk, increment]
  height_inches numeric [not null, note: 'CHECK (height_inches > 0)']
  schluter_color uuid [ref: > colors.id, note: 'Optional ref to color.']
}

Table wet_areas {
  id bigint [pk, increment]
  room_design_id uuid [not null, ref: > room_designs.id]
  layout_id uuid [note: 'NULL indicates the \'default\' entry.']

  enclosure uuid

  floor_tile_product_id uuid [not null]
  floor_tile_pattern tile_pattern_enum [not null]

  default_wall_tile_product_id uuid [not null]
  default_wall_tile_pattern tile_pattern_enum [not null]

  niche_tile_product_id uuid
  niche_tile_pattern tile_pattern_enum

  curb_tile_product_id uuid
  curb_tile_pattern tile_pattern_enum

  indexes {
    (room_design_id, layout_id) [unique]
  }

  Note: '''PostgreSQL CHECK constraint ensures curb tiling details are consistent:
  CHECK ((niche_tile_product_id IS NULL) (curb_tile_pattern IS NULL))
  CHECK ((curb_tile_product_id IS NULL) (curb_tile_pattern IS NULL))
  '''
}

Table shower_wall_overrides {
  wet_area_id bigint [not null, ref: > wet_areas.id, note: 'Links to the specific wet area instance']
  wall_layout_id uuid [not null]

  tile_product_id uuid [not null]
  tile_pattern tile_pattern_enum [not null]

  indexes {
    (wet_area_id, wall_layout_id) [unique]
  }

}