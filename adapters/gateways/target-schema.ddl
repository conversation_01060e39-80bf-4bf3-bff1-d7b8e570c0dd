CREATE SCHEMA design;

SET
    search_path TO public,
    design;

CREATE TYPE color_scheme_enum AS ENUM ('Neutral', 'Bold');

CREATE TYPE style_enum AS ENUM (
    'Traditional',
    'Transitional',
    'Mid-century',
    'Modern'
);

CREATE TYPE tile_pattern_enum AS ENUM (
    'Vertical',
    'Horizontal',
    'HalfOffset',
    'ThirdOffset',
    'Herringbone'
);

CREATE TYPE shower_head_position_enum AS ENUM ('Wall', 'Ceiling');

CREATE TYPE lighting_position_enum AS ENUM ('Top', 'Side');

CREATE TYPE rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Outdated',
    'Archived'
);

CREATE TABLE product_categories (id UUID PRIMARY KEY, name TEXT);

CREATE TABLE colors (id UUID PRIMARY KEY, name TEXT);

CREATE TABLE room_designs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    project_id TEXT,
    status TEXT,
    title TEXT,
    description TEXT,
    color_scheme color_scheme_enum,
    style style_enum
);

CREATE TABLE renditions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    status rendition_status_enum NOT NULL,
    url TEXT,
    CONSTRAINT chk_rendition_url CHECK (
        status <> 'Completed'
        OR url IS NOT NULL
    )
);

CREATE TABLE product_selections (
    id BIGSERIAL PRIMARY KEY,
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES product_categories (id),
    layout_id UUID, -- Reference to a specific instance of an item in the room layout. 
    product_id UUID NOT NULL, -- Soft ref. to product in Catalog.
    UNIQUE (room_design_id, category_id, layout_id) NULLS NOT DISTINCT
);

CREATE TABLE floor_tile (
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    layout_id UUID, -- Optional (soft) reference to a specific floor in the room layout. 
    product_id UUID NOT NULL, -- Soft ref. to tile in Catalog.
    UNIQUE (room_design_id, layout_id) NULLS NOT DISTINCT,
    pattern tile_pattern_enum NOT NULL
);

CREATE TABLE shower_systems (
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    layout_id UUID, -- Optional (soft) reference to a specific shower system in the room layout. 
    UNIQUE (room_design_id, layout_id) NULLS NOT DISTINCT,
    product_id UUID NOT NULL, -- Soft ref. to shower system in Catalog.
    position shower_head_position_enum NOT NULL
);

CREATE TABLE tubs (
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    layout_id UUID NOT NULL, -- Reference to a specific tub instance in the layout.
    -- Composite Primary Key (because layout_identifier is guaranteed unique per tub within a design):
    PRIMARY KEY (room_design_id, layout_id),
    product_id UUID NOT NULL, -- The tub product itself.
    -- Optional Tub Filler:
    filler_layout_id UUID, -- Identifier for the tub filler instance associated with this specific tub.
    filler_product_id UUID, -- Product for the tub filler associated with this specific tub.
    -- Constraint to ensure filler details are consistent:
    CONSTRAINT chk_tub_filler CHECK (
        (
            filler_layout_id IS NULL
            AND filler_product_id IS NULL
        )
        OR (
            filler_layout_id IS NOT NULL
            AND filler_product_id IS NOT NULL
        )
    ),
);

CREATE TABLE vanity_areas (
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    layout_id UUID, -- NULL indicates the 'default' entry.
    UNIQUE (room_design_id, layout_id),
    -- Vanity components:
    vanity_product_id UUID NOT NULL,
    faucet_product_id UUID NOT NULL,
    mirror_product_id UUID NOT NULL,
    lighting_product_id UUID NULL,
    lighting_position lighting_position_enum NULL,
    -- Ensure lighting details are consistent:
    CHECK (
        (
            lighting_product_id IS NULL
            AND lighting_position IS NULL
        )
        OR (
            lighting_product_id IS NOT NULL
            AND lighting_position IS NOT NULL
        )
    )
);

CREATE TABLE wainscoting (
    id GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    height_inches NUMERIC NOT NULL CHECK (height_inches > 0),
    schluter_color UUID REFERENCES colors (id), -- Optional ref to color.
);

CREATE TABLE wall_sections (
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    layout_id UUID, -- NULL indicates the 'default' entry.
    UNIQUE (room_design_id, layout_id) NULLS NOT DISTINCT,
    -- At least one paint & wallpaper must be NULL but all 3 cannot be NULL.
    paint UUID,
    wallpaper UUID,
    tile UUID,
    tile_pattern tile_pattern_enum, -- Only relevant if tile is not NULL.
    wainscoting_id bigint REFERENCES wainscoting (id) ON DELETE CASCADE,
    -- Check constraints to enforce logic:
    CHECK ((tile IS NULL) = (tile_pattern IS NULL)),
    CHECK (
        (
            wainscoting_id IS NOT NULL
            AND tile IS NOT NULL
            AND num_nonnulls (paint, wallpaper) = 1
        )
        OR (
            wainscoting_id IS NULL
            AND num_nonnulls (paint, wallpaper, tile) = 1
        )
    )
);

CREATE TABLE wet_areas (
    id bigint GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    room_design_id UUID NOT NULL REFERENCES room_designs (id) ON DELETE CASCADE,
    layout_id UUID, -- NULL indicates the 'default' entry.
    UNIQUE (room_design_id, layout_id),
    enclosure UUID,
    -- Floor Tiling:
    floor_tile_product_id UUID NOT NULL,
    floor_tile_pattern tile_pattern_enum NOT NULL,
    -- Wall Tiling:
    default_wall_tile_product_id UUID NOT NULL,
    default_wall_tile_pattern tile_pattern_enum NOT NULL,
    -- Optional Niche Tiling:
    niche_tile_product_id UUID,
    niche_tile_pattern tile_pattern_enum,
    -- Ensure niche tiling details are consistent
    CHECK (
        (niche_tile_product_id IS NULL) = (niche_tile_pattern IS NULL)
    )
    -- Optional Curb Tiling:
    curb_tile_product_id UUID,
    curb_tile_pattern tile_pattern_enum,
    -- Ensure curb tiling details are consistent.
    CHECK (
        (curb_tile_product_id IS NULL) = (curb_tile_pattern IS NULL)
    )
);

CREATE TABLE shower_wall_overrides (
    -- Links to the specific wet area (default or overriden instance) to which this wall section belongs:
    wet_area_id BIGINT NOT NULL REFERENCES wet_areas (id) ON DELETE CASCADE,
    wall_layout_id UUID NOT NULL,
    UNIQUE (wet_area_id, wall_layout_id) -- Ensures one override per wall within a wet area.
    -- Tiling details for this specific wall:
    tile_product_id UUID NOT NULL,
    tile_pattern tile_pattern_enum NOT NULL,
);