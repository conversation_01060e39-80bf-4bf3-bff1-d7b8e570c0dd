package controllers_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type mockTemplatesPresenter struct {
	mock.Mock
}

func (m *mockTemplatesPresenter) PresentError(err error) {
	m.Called(err)
}

func (m *mockTemplatesPresenter) PresentTemplate(ctx context.Context, template usecases.Template) {
	m.Called(ctx, template)
}

func (m *mockTemplatesPresenter) PresentTemplates(ctx context.Context, templates []usecases.Template) {
	m.Called(ctx, templates)
}

func TestNewTemplateRetrievalController(t *testing.T) {
	repo := &mockTemplateRepository{}
	retriever := usecases.NewTemplateRetriever(repo)
	controller := controllers.NewTemplateRetrievalController(retriever)
	assert.NotNil(t, controller)
}

func TestTemplateRetrievalController_FetchTemplate(t *testing.T) {
	ctx := context.Background()
	templateId := uuid.New()

	repo := &mockTemplateRepository{}
	presenter := &mockTemplatesPresenter{}
	retriever := usecases.NewTemplateRetriever(repo)
	controller := controllers.NewTemplateRetrievalController(retriever)

	testTemplate := usecases.Template{
		ID:   templateId,
		Name: "Test Template",
	}

	repo.On("ReadTemplate", ctx, templateId).Return(testTemplate, nil)
	presenter.On("PresentTemplate", ctx, testTemplate).Return()

	controller.FetchTemplate(ctx, templateId, presenter)

	repo.AssertExpectations(t)
	presenter.AssertExpectations(t)
}

func TestTemplateRetrievalController_FetchAllTemplates(t *testing.T) {
	ctx := context.Background()

	repo := &mockTemplateRepository{}
	presenter := &mockTemplatesPresenter{}
	retriever := usecases.NewTemplateRetriever(repo)
	controller := controllers.NewTemplateRetrievalController(retriever)

	testTemplates := []usecases.Template{
		{ID: uuid.New(), Name: "Template 1"},
		{ID: uuid.New(), Name: "Template 2"},
	}

	repo.On("ReadAllTemplates", ctx).Return(testTemplates, nil)
	presenter.On("PresentTemplates", ctx, testTemplates).Return()

	controller.FetchAllTemplates(ctx, presenter)

	repo.AssertExpectations(t)
	presenter.AssertExpectations(t)
}
