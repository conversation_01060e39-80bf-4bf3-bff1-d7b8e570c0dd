package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type TemplateRetrievalController struct {
	retriever *usecases.TemplateRetriever
}

func NewTemplateRetrievalController(retriever *usecases.TemplateRetriever) *TemplateRetrievalController {
	return &TemplateRetrievalController{retriever: retriever}
}

func (c *TemplateRetrievalController) FetchTemplate(ctx context.Context, templateId uuid.UUID, presenter usecases.TemplatesPresenter) {
	c.retriever.RetrieveTemplate(ctx, presenter, templateId)
}

func (c *TemplateRetrievalController) FetchAllTemplates(ctx context.Context, presenter usecases.TemplatesPresenter) {
	c.retriever.RetrieveAllTemplates(ctx, presenter)
}
