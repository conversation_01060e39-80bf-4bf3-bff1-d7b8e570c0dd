package controllers

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"slices"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignMutationController struct {
	db        storage
	generator textGenerator
	logger    *slog.Logger
}

func NewDesignMutationController(db storage, tg textGenerator, logger *slog.Logger) *DesignMutationController {
	if usecases.IsNil(db) {
		panic("db cannot be nil")
	}
	if usecases.IsNil(tg) {
		panic("generator cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignMutationController{db: db, generator: tg, logger: logger}
}

func (h *DesignMutationController) parseDesignId(ctx context.Context, design adapters.Design, presenter usecases.Presenter) uuid.UUID {
	designId, err := uuid.Parse(design.ID)
	if err != nil {
		h.logger.ErrorContext(ctx, "Failed to parse design ID",
			slog.String("error", err.Error()), slog.String("designId", design.ID))
		presenter.PresentError(fmt.Errorf("invalid design ID: %w", err))
		return uuid.Nil
	}
	return designId
}

func (h *DesignMutationController) CreateDesign(ctx context.Context, projectId entities.ProjectId, design adapters.Design, presenter usecases.DesignMutationOutcomePresenter) {
	h.logger.InfoContext(ctx, "Creating design",
		slog.String("title", *design.Title), slog.String("projectId", projectId.String()))
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	designs, err := h.db.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if design.ID == "" {
		design.ID = uuid.NewString()
	} else {
		h.logger.InfoContext(ctx, "Creating design with specified ID", slog.String("designId", design.ID))
		for _, d := range designs {
			if d.ID == design.ID {
				presenter.PresentError(usecases.ErrConflict)
				return
			}
		}
	}
	designId := h.parseDesignId(ctx, design, presenter)
	if designId == uuid.Nil {
		return
	}

	ucDesign, err := design.ToUsecaseDesign(projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if !ucDesign.Title.Valid || !ucDesign.Description.Valid {
		h.logger.InfoContext(ctx, "Generating title and description for new design", slog.String("designId", design.ID))
		title, description, err := h.generator.GenerateDesignTitleAndDescription(ctx, ucDesign)
		if err != nil {
			h.logger.ErrorContext(ctx, "Failed to generate title and description for new design",
				slog.String("error", err.Error()), slog.String("designId", design.ID))
		} else {
			design.Title = &title
			design.Description = &description
			ucDesign.Title = sql.NullString{String: title, Valid: true}
			ucDesign.Description = sql.NullString{String: description, Valid: true}
		}
	} else {
		h.logger.InfoContext(ctx, "Using provided title (and description) for new design",
			slog.String("designId", design.ID), slog.String("title", *design.Title))
	}

	designs = append(designs, design)
	if err := h.db.UpdateDesigns(ctx, projectId, designs); err != nil {
		presenter.PresentError(err)
		return
	}
	h.logger.InfoContext(ctx, "Created design", slog.String("designId", design.ID), slog.String("projectId", projectId.String()))
	presenter.ConveySuccessWithResource(ucDesign, usecases.Created)
}

func (h *DesignMutationController) SaveDesign(ctx context.Context, projectId entities.ProjectId, design adapters.Design, presenter usecases.DesignMutationOutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	designs, err := h.db.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	i := slices.IndexFunc(designs, func(d adapters.Design) bool {
		return d.ID == design.ID
	})
	designId := h.parseDesignId(ctx, design, presenter)
	if designId == uuid.Nil {
		return
	}
	var successStatus usecases.Status
	if i < 0 {
		designs = append(designs, design)
		h.logger.InfoContext(ctx, "Creating new design...", slog.String("designId", design.ID))
		successStatus = usecases.Created
	} else {
		designs[i] = design
		h.logger.InfoContext(ctx, "Replacing design...", slog.String("designId", design.ID))
		successStatus = usecases.Updated
	}
	if err := h.db.UpdateDesigns(ctx, projectId, designs); err != nil {
		presenter.PresentError(err)
		return
	}
	payload, err := design.ToUsecaseDesign(projectId)
	if err != nil {
		presenter.PresentError(fmt.Errorf("design saved but could not be returned: %w", err))
		return
	}
	switch successStatus {
	case usecases.Created:
		presenter.ConveySuccessWithResource(payload, usecases.Created)
	case usecases.Updated:
		if err := h.db.MarkRenderOutdated(ctx, designId); err != nil {
			h.logger.ErrorContext(ctx, "Failed to mark render as outdated",
				slog.String("designId", design.ID), slog.String("error", err.Error()))
		}
		presenter.ConveySuccessWithResource(payload, usecases.Updated)
	}
	h.logger.InfoContext(ctx, "Done.", slog.String("designId", design.ID))
}

func (h *DesignMutationController) SaveAllDesignsForProject(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design, presenter usecases.OutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	currDesignId, err := h.db.GetCurrentDesignIdForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if currDesignId == "" {
		h.logger.InfoContext(ctx, "No current design ID set for project so defaulting to 01.", slog.String("projectId", projectId.String()))
		currDesignId = "01"
	}
	currDesignIdNeedsUpdate := false
	updatedDesigns := make([]adapters.Design, 0, len(designs))
	for _, design := range designs {
		if len(design.ID) < 3 {
			designID := uuid.NewString()
			if design.ID == currDesignId {
				currDesignId = designID
				currDesignIdNeedsUpdate = true
				h.logger.InfoContext(ctx, "Current design ID matches short ID so updating to new UUID",
					slog.String("projectId", projectId.String()), slog.String("oldDesignId", design.ID), slog.String("newDesignId", designID))
			}
			design.ID = designID
		}
		updatedDesigns = append(updatedDesigns, design)
	}
	if err := h.db.UpdateDesigns(ctx, projectId, updatedDesigns); err != nil {
		presenter.PresentError(err)
		return
	}
	if currDesignIdNeedsUpdate {
		if err := h.db.UpdateCurrentDesignIdForProject(ctx, projectId, uuid.MustParse(currDesignId)); err != nil {
			presenter.PresentError(fmt.Errorf("designs saved but failed to update current design ID for project: %w", err))
			return
		}
	}
	h.logger.InfoContext(ctx, "Replaced all designs for project", slog.String("projectId", projectId.String()))
	presenter.ConveySuccess()
}

func (h *DesignMutationController) ModifyDesign(ctx context.Context, projectId entities.ProjectId, design adapters.Design, presenter usecases.DesignMutationOutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	designs, err := h.db.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	i := slices.IndexFunc(designs, func(d adapters.Design) bool {
		return d.ID == design.ID
	})
	if i < 0 {
		presenter.PresentError(usecases.ErrNotFound)
		return
	}
	designId := h.parseDesignId(ctx, design, presenter)
	if designId == uuid.Nil {
		return
	}
	h.logger.InfoContext(ctx, "Merging incoming fields into existing design", slog.String("designId", design.ID))
	designs[i] = adapters.MergeDesigns(designs[i], design)
	if err := h.db.UpdateDesigns(ctx, projectId, designs); err != nil {
		presenter.PresentError(err)
		return
	}
	h.logger.InfoContext(ctx, "Modified design", slog.String("designId", design.ID))
	if err := h.db.MarkRenderOutdated(ctx, designId); err != nil {
		h.logger.ErrorContext(ctx, "Failed to mark render as outdated",
			slog.String("designId", design.ID), slog.String("error", err.Error()))
	}
	ucDesign, err := designs[i].ToUsecaseDesign(projectId)
	if err != nil {
		h.logger.ErrorContext(ctx, "Failed to convert design to usecase design",
			slog.String("projectId", projectId.String()), slog.String("designId", design.ID), slog.String("error", err.Error()))
		presenter.ConveySuccess()
		return
	}
	presenter.ConveySuccessWithResource(ucDesign, usecases.Updated)
}

func (h *DesignMutationController) DeleteDesign(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID, presenter usecases.OutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	designs, err := h.db.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	i := slices.IndexFunc(designs, func(d adapters.Design) bool {
		return d.ID == designId.String()
	})
	if i < 0 {
		presenter.PresentError(usecases.ErrNotFound)
		return
	}
	designs = slices.Delete(designs, i, i+1)
	if err := h.db.UpdateDesigns(ctx, projectId, designs); err != nil {
		presenter.PresentError(err)
		return
	}
	h.logger.InfoContext(ctx, "Deleted design", slog.String("designId", designId.String()))
	presenter.ConveySuccess()
}
