package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignRetrievalController struct {
	retriever *usecases.DesignRetriever
}

func NewDesignRetrievalController(retriever *usecases.DesignRetriever) *DesignRetrievalController {
	return &DesignRetrievalController{retriever: retriever}
}

func (c *DesignRetrievalController) FetchDesign(ctx context.Context, designId uuid.UUID, presenter usecases.DesignsPresenter) {
	c.retriever.RetrieveDesign(ctx, presenter, designId)
}

func (h *DesignRetrievalController) FetchAllDesignsForProject(ctx context.Context, projectId entities.ProjectId, presenter usecases.DesignsPresenter) {
	h.retriever.RetrieveAllDesignsForProject(ctx, presenter, projectId)
}

func (h *DesignRetrievalController) FetchDesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId, presenter usecases.DesignsPresenter) {
	h.retriever.RetrieveDesignsForMultipleProjects(ctx, presenter, projectIds)
}

type PresetRetrievalController struct {
	retriever *usecases.PresetRetriever
}

func NewPresetRetrievalController(retriever *usecases.PresetRetriever) *PresetRetrievalController {
	return &PresetRetrievalController{retriever: retriever}
}

func (c *PresetRetrievalController) FetchPresetByTemplateId(ctx context.Context, templateId string, presenter usecases.PresetPresenter) {
	c.retriever.FindPresetByTemplateId(ctx, presenter, templateId)
}
