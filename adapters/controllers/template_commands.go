package controllers

import (
	"context"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type TemplateWriteController struct {
	creator *usecases.TemplateCreater
	logger  *slog.Logger
}

func NewTemplateWriteController(creator *usecases.TemplateCreater, logger *slog.Logger) *TemplateWriteController {
	if usecases.IsNil(creator) {
		panic("creator cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &TemplateWriteController{creator: creator, logger: logger}
}

func (c *TemplateWriteController) SaveTemplate(ctx context.Context, templateId uuid.UUID, template adapters.Template, presenter usecases.TemplateCreationOutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}

	if template.ID != templateId.String() {
		template.ID = templateId.String()
	}

	usecaseTemplate, err := adapters.ToUsecaseTemplate(template)
	if err != nil {
		c.logger.ErrorContext(ctx, "Error converting template", slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	c.creator.CreateTemplate(ctx, presenter, usecaseTemplate)
}
