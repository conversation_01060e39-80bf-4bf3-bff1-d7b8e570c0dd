package adapters

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type Preset struct {
	Id         string    `json:"id"`
	TemplateId uuid.UUID `json:"templateId"`
	UpdatedAt  time.Time `json:"updatedAt"`

	RoomLayout   entities.RoomLayout `json:"roomLayout"`
	Measurements json.RawMessage     `json:"measurements"`
	Design       Design              `json:"design"`
	Rendition    Rendition           `json:"rendition"`
}

func FromUsecasePreset(preset usecases.Preset) Preset {
	return Preset{
		Id:           preset.Id,
		UpdatedAt:    preset.UpdatedAt,
		RoomLayout:   preset.RoomLayout,
		Measurements: preset.Measurements,
		Design:       FromUsecaseDesign(preset.Design),
		Rendition:    FromDomainRendition(preset.Rendition),
	}
}
