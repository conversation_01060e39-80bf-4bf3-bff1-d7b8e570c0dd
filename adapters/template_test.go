package adapters_test

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestTemplate_AlignId(t *testing.T) {
	t.Run("should set ID when template ID is empty", func(t *testing.T) {
		template := adapters.Template{}
		expectedId := uuid.New()
		
		err := template.AlignId(expectedId.String())
		
		require.NoError(t, err)
		assert.Equal(t, expectedId, template.ID)
	})

	t.Run("should set ID when template ID is nil", func(t *testing.T) {
		template := adapters.Template{ID: uuid.Nil}
		expectedId := uuid.New()
		
		err := template.AlignId(expectedId.String())
		
		require.NoError(t, err)
		assert.Equal(t, expectedId, template.ID)
	})

	t.Run("should succeed when template ID matches URL ID", func(t *testing.T) {
		templateId := uuid.New()
		template := adapters.Template{ID: templateId}
		
		err := template.AlignId(templateId.String())
		
		require.NoError(t, err)
		assert.Equal(t, templateId, template.ID)
	})

	t.Run("should return error when template ID does not match URL ID", func(t *testing.T) {
		templateId := uuid.New()
		differentId := uuid.New()
		template := adapters.Template{ID: templateId}
		
		err := template.AlignId(differentId.String())
		
		require.Error(t, err)
		assert.Contains(t, err.Error(), "template ID mismatch")
		assert.Equal(t, templateId, template.ID) // Should remain unchanged
	})

	t.Run("should return error for invalid UUID", func(t *testing.T) {
		template := adapters.Template{}
		
		err := template.AlignId("invalid-uuid")
		
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid template UUID")
	})
}

func TestToUsecaseTemplate(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()

	t.Run("should convert valid template successfully", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:          templateId,
			Name:        "Test Template",
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
			Description: "A test template",
			Inspiration: "Test inspiration",
			Atmosphere:  []string{"cozy", "modern"},
			ColorPalette: []string{"#FFFFFF", "#000000"},
			MaterialPalette: []string{"wood", "metal"},
			VanityDict: map[string]uuid.UUID{
				"24": vanityId1,
				"36": vanityId2,
			},
			FaucetDict: map[string]uuid.UUID{
				"24": faucetId1,
				"36": faucetId2,
			},
		}
		
		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)
		
		require.NoError(t, err)
		assert.Equal(t, templateId, usecaseTemplate.ID)
		assert.Equal(t, "Test Template", usecaseTemplate.Name)
		assert.Equal(t, usecases.Neutral, usecaseTemplate.ColorScheme)
		assert.Equal(t, usecases.Modern, usecaseTemplate.Style)
		assert.Equal(t, "A test template", usecaseTemplate.Description)
		assert.Equal(t, []string{"cozy", "modern"}, usecaseTemplate.Atmosphere)
		
		// Check vanity scaling options
		assert.Len(t, usecaseTemplate.VanityScalingOptions, 2)
		assert.Equal(t, vanityId1, usecaseTemplate.VanityScalingOptions[24].VanityProductID)
		assert.Equal(t, faucetId1, usecaseTemplate.VanityScalingOptions[24].FaucetProductID)
		assert.Equal(t, vanityId2, usecaseTemplate.VanityScalingOptions[36].VanityProductID)
		assert.Equal(t, faucetId2, usecaseTemplate.VanityScalingOptions[36].FaucetProductID)
	})

	t.Run("should return error for empty ID", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:   uuid.Nil,
			Name: "Test Template",
		}
		
		_, err := adapters.ToUsecaseTemplate(adapterTemplate)
		
		require.Error(t, err)
		assert.Equal(t, usecases.ErrInvalidPayload, err)
	})

	t.Run("should return error for mismatched vanity/faucet dict lengths", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:   templateId,
			Name: "Test Template",
			VanityDict: map[string]uuid.UUID{
				"24": vanityId1,
			},
			FaucetDict: map[string]uuid.UUID{
				"24": faucetId1,
				"36": faucetId2,
			},
		}
		
		_, err := adapters.ToUsecaseTemplate(adapterTemplate)
		
		require.Error(t, err)
		assert.Equal(t, usecases.ErrInvalidPayload, err)
	})
}

func TestFromUsecaseTemplate(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()
	showerFloorTileId := uuid.New()
	wallTileId := uuid.New()

	t.Run("should convert usecase template successfully", func(t *testing.T) {
		usecaseTemplate := usecases.Template{
			ID:          templateId,
			Name:        "Test Template",
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
			Description: "A test template",
			Inspiration: "Test inspiration",
			Atmosphere:  []string{"cozy", "modern"},
			ColorPalette: []string{"#FFFFFF", "#000000"},
			MaterialPalette: []string{"wood", "metal"},
			ShowerFloorTile: &showerFloorTileId,
			WallTile: &wallTileId,
			WallTilePlacement: usecases.HalfWall,
			WallpaperPlacement: usecases.NoWallpaper,
			VanityScalingOptions: map[int]usecases.VanityScalingOption{
				24: {VanityProductID: vanityId1, FaucetProductID: faucetId1},
				36: {VanityProductID: vanityId2, FaucetProductID: faucetId2},
			},
		}
		
		adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate)
		
		assert.Equal(t, templateId, adapterTemplate.ID)
		assert.Equal(t, "Test Template", adapterTemplate.Name)
		assert.Equal(t, usecases.Neutral, adapterTemplate.ColorScheme)
		assert.Equal(t, usecases.Modern, adapterTemplate.Style)
		assert.Equal(t, "A test template", adapterTemplate.Description)
		assert.Equal(t, []string{"cozy", "modern"}, adapterTemplate.Atmosphere)
		assert.Equal(t, showerFloorTileId, adapterTemplate.ShowerFloorTile)
		assert.Equal(t, wallTileId, adapterTemplate.WallTile)
		assert.Equal(t, usecases.HalfWall, adapterTemplate.WallTilePlacement)
		assert.Equal(t, usecases.NoWallpaper, adapterTemplate.WallpaperPlacement)
		
		// Check vanity/faucet dicts
		assert.Len(t, adapterTemplate.VanityDict, 2)
		assert.Len(t, adapterTemplate.FaucetDict, 2)
		assert.Equal(t, vanityId1, adapterTemplate.VanityDict["24"])
		assert.Equal(t, faucetId1, adapterTemplate.FaucetDict["24"])
		assert.Equal(t, vanityId2, adapterTemplate.VanityDict["36"])
		assert.Equal(t, faucetId2, adapterTemplate.FaucetDict["36"])
	})
}

func TestTemplate_ConversionRoundTrip(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	faucetId1 := uuid.New()

	t.Run("should maintain data integrity through round trip conversion", func(t *testing.T) {
		originalTemplate := adapters.Template{
			ID:          templateId,
			Name:        "Test Template",
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
			Description: "A test template",
			Inspiration: "Test inspiration",
			Atmosphere:  []string{"cozy", "modern"},
			ColorPalette: []string{"#FFFFFF", "#000000"},
			MaterialPalette: []string{"wood", "metal"},
			VanityDict: map[string]uuid.UUID{
				"24": vanityId1,
			},
			FaucetDict: map[string]uuid.UUID{
				"24": faucetId1,
			},
		}
		
		// Convert to usecase and back
		usecaseTemplate, err := adapters.ToUsecaseTemplate(originalTemplate)
		require.NoError(t, err)
		
		convertedBack := adapters.FromUsecaseTemplate(usecaseTemplate)
		
		// Verify key fields are preserved
		assert.Equal(t, originalTemplate.ID, convertedBack.ID)
		assert.Equal(t, originalTemplate.Name, convertedBack.Name)
		assert.Equal(t, originalTemplate.ColorScheme, convertedBack.ColorScheme)
		assert.Equal(t, originalTemplate.Style, convertedBack.Style)
		assert.Equal(t, originalTemplate.Description, convertedBack.Description)
		assert.Equal(t, originalTemplate.Inspiration, convertedBack.Inspiration)
		assert.Equal(t, originalTemplate.Atmosphere, convertedBack.Atmosphere)
		assert.Equal(t, originalTemplate.ColorPalette, convertedBack.ColorPalette)
		assert.Equal(t, originalTemplate.MaterialPalette, convertedBack.MaterialPalette)
		assert.Equal(t, len(originalTemplate.VanityDict), len(convertedBack.VanityDict))
		assert.Equal(t, len(originalTemplate.FaucetDict), len(convertedBack.FaucetDict))
		assert.Equal(t, originalTemplate.VanityDict["24"], convertedBack.VanityDict["24"])
		assert.Equal(t, originalTemplate.FaucetDict["24"], convertedBack.FaucetDict["24"])
	})
}
