package adapters

import (
	"errors"
	"log"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type Template struct {
	ID              string               `json:"id"`
	UpdatedAt       time.Time            `json:"updatedAt,omitempty"`
	ColorScheme     usecases.ColorScheme `json:"color_scheme"`
	Style           usecases.Style       `json:"style"`
	Name            string               `json:"name"`
	ImageURL        url.URL              `json:"image_url"`
	Description     string               `json:"description"`
	Inspiration     string               `json:"inspiration"`
	Atmosphere      string               `json:"atmosphere"`
	ColorPalette    string               `json:"color_palette"`
	MaterialPalette string               `json:"material_palette"`
	Materials       Materials            `json:"materials"`

	HighlightedBrandUrls []url.URL `json:"highlighted_brand_urls"`
	VanityStorage        string    `json:"vanity_storage"`
	VanityBrand          string    `json:"vanity_brand"`
	LightingBrand        string    `json:"lighting_brand"`
	ToiletBrand          string    `json:"toilet_brand"`
	PlumbingBrand        string    `json:"plumbing_brand"`
}
type Materials struct {
	Paint              uuid.UUID                   `json:"paint"`
	Mirror             uuid.UUID                   `json:"mirror"`
	Toilet             uuid.UUID                   `json:"toilet"`
	Shelves            uuid.UUID                   `json:"shelves"`
	Lighting           uuid.UUID                   `json:"lighting"`
	FloorTile          uuid.UUID                   `json:"floorTile"`
	TubFiller          uuid.UUID                   `json:"tubFiller"`
	ShowerWallTile     uuid.UUID                   `json:"showerWallTile"`
	ShowerFloorTile    uuid.UUID                   `json:"showerFloorTile"`
	AlcoveTub          uuid.UUID                   `json:"alcoveTub"`
	FreestandingTub    uuid.UUID                   `json:"freestandingTub"`
	ShowerGlassFixed   uuid.UUID                   `json:"showerGlassFixed"`
	ShowerGlassSliding uuid.UUID                   `json:"showerGlassSliding"`
	ShowerSystemFull   uuid.UUID                   `json:"showerSystemFull"`
	ShowerSystemShower uuid.UUID                   `json:"showerSystemShower"`
	TubDoorFixed       uuid.UUID                   `json:"tubDoorFixed"`
	TubDoorSliding     uuid.UUID                   `json:"tubDoorSliding"`
	WallpaperPlacement usecases.WallpaperPlacement `json:"wallpaperPlacement"`
	Wallpaper          *uuid.UUID                  `json:"wallpaper,omitempty"`
	WallTilePlacement  usecases.WallTilePlacement  `json:"wallTilePlacement"`
	WallTile           uuid.UUID                   `json:"wallTile"`
	FaucetDict         map[string]uuid.UUID        `json:"faucetDict"`
	VanityDict         map[string]uuid.UUID        `json:"vanityDict"`
}

func ToUsecaseTemplate(t Template) (usecases.Template, error) {
	// Parse the template ID as UUID
	templateUUID, err := uuid.Parse(t.ID)
	if err != nil {
		return usecases.Template{}, usecases.ErrInvalidPayload
	}

	if len(t.Materials.VanityDict) != len(t.Materials.FaucetDict) {
		log.Printf("Vanity and faucet dicts have different lengths: %d vs %d",
			len(t.Materials.VanityDict), len(t.Materials.FaucetDict))
		return usecases.Template{}, usecases.ErrInvalidPayload
	}

	// Parse atmosphere and color/material palettes from strings to arrays
	atmosphere := []string{}
	if t.Atmosphere != "" {
		atmosphere = strings.Split(strings.TrimSpace(t.Atmosphere), ", ")
	}
	colorPalette := []string{}
	if t.ColorPalette != "" {
		colorPalette = strings.Split(strings.TrimSpace(t.ColorPalette), ", ")
	}
	materialPalette := []string{}
	if t.MaterialPalette != "" {
		materialPalette = strings.Split(strings.TrimSpace(t.MaterialPalette), ", ")
	}

	templateUsecase := usecases.Template{
		ID:                   templateUUID,
		UpdatedAt:            t.UpdatedAt,
		ColorScheme:          t.ColorScheme,
		Style:                t.Style,
		Name:                 t.Name,
		ImageURL:             t.ImageURL,
		Description:          t.Description,
		Inspiration:          t.Inspiration,
		Atmosphere:           atmosphere,
		ColorPalette:         colorPalette,
		MaterialPalette:      materialPalette,
		HighlightedBrandUrls: t.HighlightedBrandUrls,
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: &t.Materials.FloorTile,
			Lighting:  &t.Materials.Lighting,
			Mirror:    &t.Materials.Mirror,
			Paint:     &t.Materials.Paint,
			Shelving:  &t.Materials.Shelves,
			Toilet:    &t.Materials.Toilet,
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          t.Materials.AlcoveTub,
			FreestandingTub:    t.Materials.FreestandingTub,
			ShowerGlassFixed:   t.Materials.ShowerGlassFixed,
			ShowerGlassSliding: t.Materials.ShowerGlassSliding,
			ShowerSystemCombo:  t.Materials.ShowerSystemFull,
			ShowerSystemSolo:   t.Materials.ShowerSystemShower,
			TubDoorFixed:       t.Materials.TubDoorFixed,
			TubDoorSliding:     t.Materials.TubDoorSliding,
		},
		TemplateProvenance: usecases.TemplateProvenance{
			LightingBrand: &t.LightingBrand,
			PlumbingBrand: &t.PlumbingBrand,
			ToiletBrand:   &t.ToiletBrand,
			VanityBrand:   &t.VanityBrand,
			VanityStorage: &t.VanityStorage,
		},
		ShowerFloorTile:    &t.Materials.ShowerFloorTile,
		ShowerWallTile:     &t.Materials.ShowerWallTile,
		TubFiller:          &t.Materials.TubFiller,
		WallTilePlacement:  t.Materials.WallTilePlacement,
		WallTile:           &t.Materials.WallTile,
		WallpaperPlacement: t.Materials.WallpaperPlacement,
	}

	// Handle wallpaper if present
	templateUsecase.Wallpaper = t.Materials.Wallpaper

	// Vanity scaling options
	templateUsecase.VanityScalingOptions = make(map[int]usecases.VanityScalingOption)
	for k, v := range t.Materials.VanityDict {
		minVanityLengthInches, err := strconv.Atoi(k)
		if err != nil {
			continue
		}
		templateUsecase.VanityScalingOptions[minVanityLengthInches] = usecases.VanityScalingOption{
			VanityProductID: v,
			FaucetProductID: t.Materials.FaucetDict[k],
		}
	}

	return templateUsecase, nil
}

// FromUsecaseTemplate converts a usecases.Template domain entity to a Template API model.
func FromUsecaseTemplate(t usecases.Template) Template {
	uuidPtrToUUID := func(u *uuid.UUID) uuid.UUID {
		if u == nil {
			return uuid.Nil
		}
		return *u
	}
	stringPtrToString := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	// Convert arrays to comma-separated strings
	atmosphereStr := strings.Join(t.Atmosphere, ", ")
	colorPaletteStr := strings.Join(t.ColorPalette, ", ")
	materialPaletteStr := strings.Join(t.MaterialPalette, ", ")

	template := Template{
		ID:                   t.ID.String(),
		UpdatedAt:            t.UpdatedAt,
		Style:                t.Style,
		ColorScheme:          t.ColorScheme,
		Name:                 t.Name,
		Description:          t.Description,
		Atmosphere:           atmosphereStr,
		ColorPalette:         colorPaletteStr,
		MaterialPalette:      materialPaletteStr,
		Inspiration:          t.Inspiration,
		PlumbingBrand:        stringPtrToString(t.TemplateProvenance.PlumbingBrand),
		LightingBrand:        stringPtrToString(t.TemplateProvenance.LightingBrand),
		VanityBrand:          stringPtrToString(t.TemplateProvenance.VanityBrand),
		ToiletBrand:          stringPtrToString(t.TemplateProvenance.ToiletBrand),
		VanityStorage:        stringPtrToString(t.TemplateProvenance.VanityStorage),
		ImageURL:             t.ImageURL,
		HighlightedBrandUrls: t.HighlightedBrandUrls,
	}

	// Set materials
	template.Materials.Paint = uuidPtrToUUID(t.FixedProductSelections.Paint)
	template.Materials.Mirror = uuidPtrToUUID(t.FixedProductSelections.Mirror)
	template.Materials.Toilet = uuidPtrToUUID(t.FixedProductSelections.Toilet)
	template.Materials.Shelves = uuidPtrToUUID(t.FixedProductSelections.Shelving)
	template.Materials.Lighting = uuidPtrToUUID(t.FixedProductSelections.Lighting)
	template.Materials.WallTile = uuidPtrToUUID(t.WallTile)
	template.Materials.FloorTile = uuidPtrToUUID(t.FixedProductSelections.FloorTile)
	template.Materials.TubFiller = uuidPtrToUUID(t.TubFiller)
	template.Materials.ShowerWallTile = uuidPtrToUUID(t.ShowerWallTile)
	template.Materials.ShowerFloorTile = uuidPtrToUUID(t.ShowerFloorTile)
	template.Materials.AlcoveTub = t.ProductSelectionOptions.AlcoveTub
	template.Materials.FreestandingTub = t.ProductSelectionOptions.FreestandingTub
	template.Materials.ShowerGlassFixed = t.ProductSelectionOptions.ShowerGlassFixed
	template.Materials.ShowerGlassSliding = t.ProductSelectionOptions.ShowerGlassSliding
	template.Materials.ShowerSystemFull = t.ProductSelectionOptions.ShowerSystemCombo
	template.Materials.ShowerSystemShower = t.ProductSelectionOptions.ShowerSystemSolo
	template.Materials.TubDoorFixed = t.ProductSelectionOptions.TubDoorFixed
	template.Materials.TubDoorSliding = t.ProductSelectionOptions.TubDoorSliding
	template.Materials.WallTilePlacement = t.WallTilePlacement
	template.Materials.WallpaperPlacement = t.WallpaperPlacement
	template.Materials.Wallpaper = t.Wallpaper

	// Convert vanity scaling options back to dicts
	template.Materials.VanityDict = make(map[string]uuid.UUID)
	template.Materials.FaucetDict = make(map[string]uuid.UUID)
	for minLength, option := range t.VanityScalingOptions {
		key := strconv.Itoa(minLength)
		template.Materials.VanityDict[key] = option.VanityProductID
		template.Materials.FaucetDict[key] = option.FaucetProductID
	}

	return template
}

// AlignId aligns the template ID with the provided ID from the URL path.
// If the template ID is empty, it sets it to the provided ID.
// If the template ID is different from the provided ID, it returns an error.
func (t *Template) AlignId(id string) error {
	_, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid template UUID")
	}

	if t.ID == "" {
		t.ID = id
	} else if t.ID != id {
		return errors.New("template ID mismatch")
	}
	return nil
}
