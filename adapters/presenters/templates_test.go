package presenters_test

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewTemplatesPresenter(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()

	presenter := presenters.NewTemplatesPresenter(logger, w)
	assert.NotNil(t, presenter)
}

func TestTemplatesPresenter_PresentTemplate(t *testing.T) {
	ctx := context.Background()
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplatesPresenter(logger, w)

	testTemplate := usecases.Template{
		ID:          uuid.New(),
		Name:        "Test Template",
		ColorScheme: usecases.Neutral,
		Style:       usecases.Modern,
		Description: "A test template",
		Inspiration: "Test inspiration",
	}

	presenter.PresentTemplate(ctx, testTemplate)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))

	var responseTemplate usecases.Template
	err := json.Unmarshal(w.Body.Bytes(), &responseTemplate)
	require.NoError(t, err)
	assert.Equal(t, testTemplate.ID, responseTemplate.ID)
	assert.Equal(t, testTemplate.Name, responseTemplate.Name)
}

func TestTemplatesPresenter_PresentTemplates(t *testing.T) {
	ctx := context.Background()
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplatesPresenter(logger, w)

	testTemplates := []usecases.Template{
		{
			ID:          uuid.New(),
			Name:        "Template 1",
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		{
			ID:          uuid.New(),
			Name:        "Template 2",
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
	}

	presenter.PresentTemplates(ctx, testTemplates)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	var responseTemplates []usecases.Template
	err := json.Unmarshal(w.Body.Bytes(), &responseTemplates)
	require.NoError(t, err)
	assert.Len(t, responseTemplates, 2)
	assert.Equal(t, testTemplates[0].Name, responseTemplates[0].Name)
	assert.Equal(t, testTemplates[1].Name, responseTemplates[1].Name)
}

func TestNewTemplateCreationOutcomePresenter(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()

	presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)
	assert.NotNil(t, presenter)
}

func TestTemplateCreationOutcomePresenter_ConveySuccessWithNewResource(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)

	testTemplate := usecases.Template{
		ID:              uuid.New(),
		Name:            "Test Template",
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Description:     "A test template",
		Inspiration:     "Test inspiration",
		Atmosphere:      []string{"cozy", "modern"},
		ColorPalette:    []string{"#FFFFFF", "#000000"},
		MaterialPalette: []string{"wood", "metal"},
	}

	presenter.ConveySuccessWithNewResource(testTemplate)

	assert.Equal(t, http.StatusCreated, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
	assert.Contains(t, w.Header().Get("Location"), testTemplate.ID.String())

	// Verify response body contains the template
	var responseBody map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &responseBody)
	require.NoError(t, err)
	assert.Equal(t, testTemplate.ID.String(), responseBody["id"])
	assert.Equal(t, testTemplate.Name, responseBody["name"])
}

func TestTemplateCreationOutcomePresenter_PresentError(t *testing.T) {
	logger := slog.Default()

	testCases := []struct {
		name           string
		error          error
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "invalid payload error",
			error:          usecases.ErrInvalidPayload,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "invalid payload",
		},
		{
			name:           "not found error",
			error:          usecases.ErrNotFound,
			expectedStatus: http.StatusNotFound,
			expectedBody:   "not found",
		},
		{
			name:           "conflict error",
			error:          usecases.ErrConflict,
			expectedStatus: http.StatusConflict,
			expectedBody:   "Template with the same ID already exists",
		},
		{
			name:           "generic error",
			error:          assert.AnError,
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   assert.AnError.Error(),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)

			presenter.PresentError(tc.error)

			assert.Equal(t, tc.expectedStatus, w.Code)
			assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
			assert.Contains(t, w.Body.String(), tc.expectedBody)
		})
	}
}

func TestTemplateCreationOutcomePresenter_ConveySuccessWithNewResource_InvalidTemplate(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewTemplateCreationOutcomePresenter(logger, w)

	// Template with nil/zero UUID
	invalidTemplate := usecases.Template{
		ID:   uuid.Nil,
		Name: "Invalid Template",
	}

	presenter.ConveySuccessWithNewResource(invalidTemplate)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Contains(t, w.Body.String(), "Missing/invalid ID in created template")
}
