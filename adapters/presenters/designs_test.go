package presenters_test

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/cacticloud/cactikit/tools/pretty"
	"github.com/go-json-experiment/json/jsontext"
	"github.com/google/uuid"
	"github.com/kodeart/go-problem/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lastUpdated := "2025-06-13T00:59:59Z"
	none := usecases.NoWallpaper
	halfWall := usecases.HalfWall
	defaultFloorTilePattern := usecases.HorizontalStacked
	nope := false
	return adapters.Design{
		ID:                   uuid.NewString(),
		FloorTile:            &floorTile,
		Toilet:               &toilet,
		Vanity:               &vanity,
		Faucet:               &faucet,
		Mirror:               &mirror,
		LastUpdatedDateTime:  &lastUpdated,
		WallpaperPlacement:   &none,
		WallTilePlacement:    &halfWall,
		FloorTilePattern:     &defaultFloorTilePattern,
		IsShowerGlassVisible: &nope,
		IsTubDoorVisible:     &nope,
		IsNichesVisible:      &nope,
	}
}

func TestFetchingDesigns(t *testing.T) {
	testDesigns := []adapters.Design{genDesign(), genDesign(), genDesign()}
	data, err := json.Marshal(testDesigns)
	if err != nil {
		t.Fatal(err)
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentData(context.Background(), testDesigns)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}

func TestFetchingSingleDesign(t *testing.T) {
	testDesign := genDesign()
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentData(context.Background(), testDesign)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}

func TestFetchingMixedResults(t *testing.T) {
	testDesigns1 := []adapters.Design{genDesign(), genDesign()}
	testDesigns2 := make([]usecases.Design, len(testDesigns1))
	var err error
	for i, d := range testDesigns1 {
		testDesigns2[i], err = d.ToUsecaseDesign("PRJ-FOOBAR")
		require.NoError(t, err)
	}
	designs := map[entities.ProjectId][]usecases.Design{
		"PRJ-FOOBAR": testDesigns2,
	}
	errors := []error{usecases.ErrNotFound}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentDesignsByProject(context.Background(), designs, errors)
	if status := recorder.Code; status != http.StatusMultiStatus {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
	var errList []*problem.Problem
	for _, err := range errors {
		errList = append(errList, problem.New().WithStatus(http.StatusNotFound).WithDetail(err.Error()))
	}
	results := map[entities.ProjectId]presenters.MultiProjectOutputItem{
		"PRJ-FOOBAR": {
			Status: http.StatusOK,
			Data:   testDesigns1,
		},
		"errors": {
			Status: http.StatusNotFound,
			Errors: errList,
		},
	}
	data, err := json.Marshal(results)
	if err != nil {
		t.Fatal(err)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		(*jsontext.Value)(&data).Indent()
		expected = string(data)
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", pretty.FormatJSON([]byte(body)), expected)
	}
}

// Boundary cases and error conditions tests

func TestDesignsPresenter_PresentError(t *testing.T) {
	tests := []struct {
		name           string
		err            error
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "generic error",
			err:            errors.New("something went wrong"),
			expectedStatus: http.StatusNotFound,
			expectedBody:   "something went wrong\n",
		},
		{
			name:           "empty error message",
			err:            errors.New(""),
			expectedStatus: http.StatusNotFound,
			expectedBody:   "\n",
		},
		{
			name:           "usecases error",
			err:            usecases.ErrNotFound,
			expectedStatus: http.StatusNotFound,
			expectedBody:   "target not found\n",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			recorder := httptest.NewRecorder()
			presenter := presenters.NewDesignsPresenter(nil, recorder)

			presenter.PresentError(tt.err)

			assert.Equal(t, tt.expectedStatus, recorder.Code)
			assert.Equal(t, tt.expectedBody, recorder.Body.String())
		})
	}
}

// Note: PresentError method does not handle nil errors and will panic.
// This could be considered a bug in the implementation.
func TestDesignsPresenter_PresentError_NilErrorPanics(t *testing.T) {
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)

	assert.Panics(t, func() {
		presenter.PresentError(nil)
	}, "PresentError should panic when given a nil error")
}

func TestDesignsPresenter_PresentData_ErrorCases(t *testing.T) {
	t.Run("unmarshalable data", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(CreateTestLogger(), recorder)

		// Create data that cannot be marshaled to JSON (circular reference)
		data := UnmarshalableData()

		presenter.PresentData(context.Background(), data)

		assert.Equal(t, http.StatusInternalServerError, recorder.Code)
		assert.Contains(t, recorder.Body.String(), "json: unsupported value")
	})

	t.Run("write failure", func(t *testing.T) {
		failingWriter := NewMockFailingResponseWriter()
		failingWriter.ShouldFailOnWrite = true
		presenter := presenters.NewDesignsPresenter(CreateTestLogger(), failingWriter)

		presenter.PresentData(context.Background(), "simple data")

		// Should set CORS header and attempt to set content type, but write failure triggers http.Error
		AssertCORSHeaders(t, failingWriter.ResponseRecorder)
		// After http.Error is called, content type becomes text/plain
		assert.Equal(t, "text/plain; charset=utf-8", failingWriter.Header().Get("Content-Type"))
		assert.Equal(t, http.StatusInternalServerError, failingWriter.Code)
	})

	t.Run("nil data", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(nil, recorder)

		presenter.PresentData(context.Background(), nil)

		assert.Equal(t, http.StatusOK, recorder.Code)
		assert.Equal(t, "null", recorder.Body.String())
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"))
	})

	t.Run("empty slice", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(nil, recorder)

		presenter.PresentData(context.Background(), []adapters.Design{})

		assert.Equal(t, http.StatusOK, recorder.Code)
		assert.Equal(t, "[]", recorder.Body.String())
	})
}

func TestDesignsPresenter_PresentDesigns_BoundaryCases(t *testing.T) {
	t.Run("empty designs slice", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(nil, recorder)

		presenter.PresentDesigns(context.Background(), []usecases.Design{})

		assert.Equal(t, http.StatusOK, recorder.Code)
		assert.Equal(t, "[]", recorder.Body.String())
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"))
	})

	t.Run("nil designs slice", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(nil, recorder)

		presenter.PresentDesigns(context.Background(), nil)

		assert.Equal(t, http.StatusOK, recorder.Code)
		// PresentDesigns creates an empty slice when given nil, so result is []
		assert.Equal(t, "[]", recorder.Body.String())
	})
}

func TestDesignsPresenter_PresentDesignsByProject_BoundaryCases(t *testing.T) {
	t.Run("empty data map with no errors", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(nil, recorder)

		data := make(map[entities.ProjectId][]usecases.Design)
		errors := []error{}

		presenter.PresentDesignsByProject(context.Background(), data, errors)

		assert.Equal(t, http.StatusMultiStatus, recorder.Code)
		assert.Equal(t, "{}", recorder.Body.String())
		assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"))
		assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"))
	})

	t.Run("nil data map with errors", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(nil, recorder)

		errors := []error{usecases.ErrNotFound, errors.New("custom error")}

		presenter.PresentDesignsByProject(context.Background(), nil, errors)

		assert.Equal(t, http.StatusMultiStatus, recorder.Code)

		var result map[entities.ProjectId]presenters.MultiProjectOutputItem
		err := json.Unmarshal(recorder.Body.Bytes(), &result)
		require.NoError(t, err)

		assert.Contains(t, result, entities.ProjectId("errors"))
		errorItem := result["errors"]
		assert.Equal(t, http.StatusNotFound, errorItem.Status)
		assert.Len(t, errorItem.Errors, 2)
		assert.Equal(t, "target not found", errorItem.Errors[0].Detail)
		assert.Equal(t, "custom error", errorItem.Errors[1].Detail)
	})

	t.Run("data with empty design slices", func(t *testing.T) {
		recorder := httptest.NewRecorder()
		presenter := presenters.NewDesignsPresenter(nil, recorder)

		data := map[entities.ProjectId][]usecases.Design{
			"PRJ-1": {},
			"PRJ-2": {},
		}

		presenter.PresentDesignsByProject(context.Background(), data, nil)

		assert.Equal(t, http.StatusMultiStatus, recorder.Code)

		var result map[entities.ProjectId]presenters.MultiProjectOutputItem
		err := json.Unmarshal(recorder.Body.Bytes(), &result)
		require.NoError(t, err)

		assert.Len(t, result, 2)
		assert.Equal(t, http.StatusOK, result["PRJ-1"].Status)
		assert.Empty(t, result["PRJ-1"].Data)
		assert.Equal(t, http.StatusOK, result["PRJ-2"].Status)
		assert.Empty(t, result["PRJ-2"].Data)
	})
}

func TestUsePascalCaseTilePatterns(t *testing.T) {
	tests := []struct {
		name     string
		input    adapters.Design
		expected adapters.Design
	}{
		{
			name: "all tile patterns present",
			input: adapters.Design{
				ID:                     "test-id",
				FloorTilePattern:       stringPtr("horizontal"),
				ShowerFloorTilePattern: stringPtr("vertical"),
				ShowerWallTilePattern:  stringPtr("herringbone"),
				WallTilePattern:        stringPtr("halfOffset"),
			},
			expected: adapters.Design{
				ID:                     "test-id",
				FloorTilePattern:       stringPtr("Horizontal"),
				ShowerFloorTilePattern: stringPtr("Vertical"),
				ShowerWallTilePattern:  stringPtr("Herringbone"),
				WallTilePattern:        stringPtr("HalfOffset"),
			},
		},
		{
			name: "nil tile patterns",
			input: adapters.Design{
				ID:                     "test-id",
				FloorTilePattern:       nil,
				ShowerFloorTilePattern: nil,
				ShowerWallTilePattern:  nil,
				WallTilePattern:        nil,
			},
			expected: adapters.Design{
				ID:                     "test-id",
				FloorTilePattern:       nil,
				ShowerFloorTilePattern: nil,
				ShowerWallTilePattern:  nil,
				WallTilePattern:        nil,
			},
		},
		{
			name: "empty string tile patterns",
			input: adapters.Design{
				ID:                     "test-id",
				FloorTilePattern:       stringPtr(""),
				ShowerFloorTilePattern: stringPtr(""),
				ShowerWallTilePattern:  stringPtr(""),
				WallTilePattern:        stringPtr(""),
			},
			expected: adapters.Design{
				ID:                     "test-id",
				FloorTilePattern:       stringPtr(""),
				ShowerFloorTilePattern: stringPtr(""),
				ShowerWallTilePattern:  stringPtr(""),
				WallTilePattern:        stringPtr(""),
			},
		},
		{
			name: "unicode characters",
			input: adapters.Design{
				ID:               "test-id",
				FloorTilePattern: stringPtr("ñice"),
				WallTilePattern:  stringPtr("ümlauts"),
			},
			expected: adapters.Design{
				ID:               "test-id",
				FloorTilePattern: stringPtr("Ñice"),
				WallTilePattern:  stringPtr("Ümlauts"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := presenters.UsePascalCaseTilePatterns(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Helper function to create string pointers for tile patterns
func stringPtr(s string) *usecases.TilePattern {
	tp := usecases.TilePattern(s)
	return &tp
}
