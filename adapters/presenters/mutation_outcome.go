package presenters

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignMutationOutcomePresenter struct {
	w      http.ResponseWriter
	logger *slog.Logger
}

func NewDesignMutationOutcomePresenter(logger *slog.Logger, w http.ResponseWriter) *DesignMutationOutcomePresenter {
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignMutationOutcomePresenter{logger: logger, w: w}
}

func (p *DesignMutationOutcomePresenter) PresentError(err error) {
	p.logger.Error("Error mutating design", slog.String("error message", err.Error()))
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	switch err {
	case usecases.ErrInvalidPayload:
		http.Error(p.w, err.Error(), http.StatusBadRequest)
		return
	case usecases.ErrNotFound:
		http.Error(p.w, err.Error(), http.StatusNotFound)
		return
	case usecases.ErrConflict:
		http.Error(p.w, "Design with the same ID already exists", http.StatusConflict)
		return
	default:
		http.Error(p.w, err.Error(), http.StatusInternalServerError)
		return
	}
}

func (p *DesignMutationOutcomePresenter) ConveySuccessWithResource(design usecases.Design, status usecases.Status) {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	var zeroUUID uuid.UUID
	if design.ID == uuid.Nil || design.ID == zeroUUID {
		http.Error(p.w, "Missing/invalid ID in created design", http.StatusInternalServerError)
		return
	}
	if design.ProjectID == "" {
		http.Error(p.w, "Missing project ID in created design", http.StatusInternalServerError)
		return
	}
	p.w.Header().Set("Location", fmt.Sprintf("/projects/%s/designs/%s", design.ProjectID, design.ID))
	p.w.Header().Set("Content-Type", "application/json")
	switch status {
	case usecases.Created:
		p.w.WriteHeader(http.StatusCreated)
	case usecases.Updated:
		p.w.WriteHeader(http.StatusOK)
	}
	output := adapters.FromUsecaseDesign(design)
	response, err := json.MarshalIndent(output, "", "  ")
	if err != nil {
		http.Error(p.w, err.Error(), http.StatusInternalServerError)
		return
	}
	p.w.Write(response)
}

func (p *DesignMutationOutcomePresenter) ConveySuccess() {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	p.w.WriteHeader(http.StatusNoContent)
}
