-- migrate:up
CREATE SCHEMA layout;

SET
    search_path TO public,
    template,
    design,
    layout;

CREATE TABLE layout.room_layouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    raw_data JSONB NOT NULL,
    xxhash BYTEA NOT NULL,
    CONSTRAINT chk_room_layouts_raw_data CHECK (jsonb_typeof (raw_data) = 'object')
);

CREATE INDEX ON layout.room_layouts (xxhash);

CREATE TRIGGER refresh_layouts_updated_at BEFORE
UPDATE ON layout.room_layouts FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column ();

-- migrate:down
DROP SCHEMA IF EXISTS layout CASCADE;