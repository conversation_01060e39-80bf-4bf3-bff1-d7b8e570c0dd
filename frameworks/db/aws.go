package db

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
)

type secret struct {
	Password string `json:"password"`
}

func GetPostgresPassword(ctx context.Context) (string, error) {
	pwd := os.Getenv("PGPASSWORD")
	if pwd != "" {
		return pwd, nil
	}

	secretName := os.Getenv("DATABASE_CREDS_SECRET_NAME")
	if secretName == "" {
		return "", errors.New("PGPASSWORD is not set and DATABASE_CREDS_SECRET_NAME env variable is missing")
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-west-2"))
	if err != nil {
		return "", fmt.Errorf("failed to load AWS config: %v", err)
	}
	svc := secretsmanager.NewFromConfig(cfg)

	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	}
	result, err := svc.GetSecretValue(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to get secret value: %v", err)
	}

	var secret secret
	if err := json.Unmarshal([]byte(*result.SecretString), &secret); err != nil {
		return "", fmt.Errorf("failed to parse secret JSON: %v", err)
	}
	if secret.Password == "" {
		return "", errors.New("Password field not found in the secret")
	}

	return secret.Password, nil
}
