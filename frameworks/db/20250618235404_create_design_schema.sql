-- migrate:up
CREATE SCHEMA design;

SET
    search_path TO public,
    template,
    layout,
    design;

CREATE TABLE design.room_designs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    project_id TEXT,
    room_layout_id UUID REFERENCES layout.room_layouts (id) ON DELETE CASCADE,
    status TEXT,
    title TEXT,
    description TEXT,
    color_scheme color_scheme_enum,
    style style_enum
);

CREATE INDEX ON design.room_designs (project_id);

CREATE INDEX ON design.room_designs (room_layout_id);

CREATE TRIGGER refresh_designs_updated_at BEFORE
UPDATE ON design.room_designs FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column ();

CREATE TABLE design.default_products (
    room_design_id UUID PRIMARY KEY REFERENCES design.room_designs (id) ON DELETE CASCADE,
    floor_tile UUID,
    floor_tile_pattern tile_pattern_enum,
    paint UUID,
    toilet UUID,
    vanity UUID,
    faucet UUID,
    mirror UUID,
    lighting UUID,
    shelving UUID,
    wall_tile_placement TEXT NOT NULL,
    wall_tile UUID,
    wall_tile_pattern tile_pattern_enum,
    wallpaper_placement TEXT NOT NULL,
    wallpaper UUID,
    shower_system UUID,
    shower_floor_tile UUID,
    shower_floor_tile_pattern tile_pattern_enum,
    shower_wall_tile UUID,
    shower_wall_tile_pattern tile_pattern_enum,
    shower_short_wall_tile UUID,
    shower_glass UUID,
    niche_tile UUID,
    tub UUID,
    tub_filler UUID,
    tub_door UUID
);

CREATE TABLE design.render_prefs (
    room_design_id UUID PRIMARY KEY REFERENCES design.room_designs (id) ON DELETE CASCADE,
    shower_glass_visible BOOLEAN NOT NULL DEFAULT FALSE,
    tub_door_visible BOOLEAN NOT NULL DEFAULT FALSE,
    niches_visible BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE design.retail_info (
    room_design_id UUID PRIMARY KEY REFERENCES design.room_designs (id) ON DELETE CASCADE,
    total_price_cents posint,
    lead_time_days posint,
    sku_count posint
);

-- migrate:down
DROP SCHEMA IF EXISTS design CASCADE;