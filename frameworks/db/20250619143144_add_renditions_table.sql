-- migrate:up
CREATE TYPE public.rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Outdated',
    'Archived'
);

CREATE TABLE public.renditions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    room_design_id UUID NOT NULL REFERENCES design.room_designs (id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
    status public.rendition_status_enum NOT NULL,
    url http_img_url,
    CONSTRAINT chk_rendition_url CHECK (
        status <> 'Completed'
        OR url IS NOT NULL
    )
);

CREATE INDEX ON public.renditions (room_design_id);

CREATE TRIGGER refresh_renditions_updated_at BEFORE
UPDATE ON public.renditions FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column ();

ALTER TABLE public.legacy_lookup
ADD COLUMN rendition_id UUID UNIQUE REFERENCES public.renditions (id) ON DELETE CASCADE;

-- migrate:down
ALTER TABLE public.legacy_lookup
DROP COLUMN rendition_id;

DROP TRIGGER IF EXISTS refresh_renditions_updated_at ON public.renditions;

DROP TABLE IF EXISTS public.renditions;

DROP TYPE IF EXISTS rendition_status_enum;