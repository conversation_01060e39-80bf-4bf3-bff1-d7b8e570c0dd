-- migrate:up
CREATE DOMAIN posint AS integer CHECK (VALUE > 0);
CREATE DOMAIN http_img_url AS text CHECK (VALUE ~ '^https?://.*\.(webp|jpe?g|png)$');

CREATE TYPE color_scheme_enum AS ENUM ('Neutral', 'Bold');
CREATE TYPE style_enum AS ENUM (
    'Traditional',
    'Transitional',
    'Mid-century',
    'Modern'
);
CREATE TYPE tile_pattern_enum AS ENUM (
    'Vertical',
    'Horizontal',
    'HalfOffset',
    'ThirdOffset',
    'Herringbone'
);

CREATE OR REPLACE FUNCTION maintain_updated_at_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = NOW();
RETURN NEW;
END;
$$LANGUAGE plpgsql;

-- migrate:down
DROP FUNCTION IF EXISTS maintain_updated_at_column();

DROP TYPE IF EXISTS tile_pattern_enum;
DROP TYPE IF EXISTS style_enum;
DROP TYPE IF EXISTS color_scheme_enum;

DROP DOMAIN IF EXISTS http_img_url;
DROP DOMAIN IF EXISTS posint;
