package web

import (
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type PresetQueryHandler struct {
	logger            *slog.Logger
	defaultRoomLayout json.RawMessage
	presetController  *controllers.PresetRetrievalController
}

func NewPresetQueryHandler(logger *slog.Logger, defaultRoomLayout json.RawMessage, pc *controllers.PresetRetrievalController) *PresetQueryHandler {
	if pc == nil {
		panic("preset controller cannot be nil")
	}
	if defaultRoomLayout == nil {
		panic("default room layout cannot be empty")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &PresetQueryHandler{logger: logger, defaultRoomLayout: defaultRoomLayout, presetController: pc}
}
func (h *PresetQueryHandler) HandleGetPreset(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	templateId := r.PathValue("templateId")
	pp := presenters.NewPresetPresenter(w, h.logger, h.defaultRoomLayout)
	switch len(templateId) {
	case 2: // Legacy ID
		h.presetController.FetchPresetByTemplateId(ctx, templateId, pp)
	default:
		http.Error(w, "Invalid preset ID", http.StatusBadRequest)
		return
	}
}

type DesignQueryHandler struct {
	logger           *slog.Logger
	designController *controllers.DesignRetrievalController
}

func NewDesignQueryHandler(logger *slog.Logger, dc *controllers.DesignRetrievalController) *DesignQueryHandler {
	if dc == nil {
		panic("design controller cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignQueryHandler{logger: logger, designController: dc}
}

func (h *DesignQueryHandler) HandleListDesignsForProject(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	projectId := r.PathValue("projectId")
	h.designController.FetchAllDesignsForProject(ctx, entities.NewProjectId(projectId), dp)
}

func (h *DesignQueryHandler) HandleGetSpecifiedDesign(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	dp := presenters.NewDesignsPresenter(h.logger, w)
	h.designController.FetchDesign(ctx, designUUID, dp)
}

func (h *DesignQueryHandler) HandleGetAllDesignsForMultipleProjects(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	log.Println("URL query: ", r.URL.Query())
	h.logger.DebugContext(ctx, "Handling GET request for multiple projects...", slog.String("url", r.URL.RawQuery))
	projectIdsParam := r.URL.Query().Get("ids")
	if projectIdsParam == "" {
		http.Error(w, "No project IDs provided", http.StatusBadRequest)
		return
	}
	h.logger.InfoContext(ctx, "Designs for multiple projects requested", slog.String("projectIds", projectIdsParam))
	var projectIds []entities.ProjectId
	for id := range strings.SplitSeq(projectIdsParam, ",") {
		projectId := entities.NewProjectId(id)
		if !projectId.IsValid() {
			http.Error(w, fmt.Sprintf("Invalid project ID: '%v'.", projectId.String()), http.StatusBadRequest)
			return
		}
		projectIds = append(projectIds, projectId)
	}
	h.logger.InfoContext(ctx, "Fetching designs for multiple projects", slog.Int("count", len(projectIds)))
	h.designController.FetchDesignsForMultipleProjects(ctx, projectIds, dp)
}
