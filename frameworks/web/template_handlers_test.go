package web_test

import (
	"bytes"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
)

// Tests for TemplateWriteHandler error cases
func TestTemplateWriteHandler_ErrorCases(t *testing.T) {
	logger := slog.Default()
	// Create a handler with logger but no controller for testing error cases
	handler := web.NewTemplateWriteHandler(logger, nil)

	templateId := uuid.New()

	t.Run("should return 400 for invalid template UUID", func(t *testing.T) {
		req := httptest.NewRequest("PUT", "/templates/invalid-uuid", bytes.NewReader([]byte("{}")))
		req.SetPathValue("templateId", "invalid-uuid")
		w := httptest.NewRecorder()

		handler.HandlePutTemplate(w, req)

		if w.Code != http.StatusBadRequest {
			t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
		}
		if !bytes.Contains(w.Body.Bytes(), []byte("Invalid template UUID")) {
			t.Errorf("Expected error message about invalid UUID, got: %s", w.Body.String())
		}
	})

	t.Run("should return 400 for empty request body", func(t *testing.T) {
		req := httptest.NewRequest("PUT", "/templates/"+templateId.String(), bytes.NewReader([]byte{}))
		req.SetPathValue("templateId", templateId.String())
		w := httptest.NewRecorder()

		handler.HandlePutTemplate(w, req)

		if w.Code != http.StatusBadRequest {
			t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
		}
		if !bytes.Contains(w.Body.Bytes(), []byte("empty request body")) {
			t.Errorf("Expected error message about empty body, got: %s", w.Body.String())
		}
	})

	t.Run("should return 400 for invalid JSON", func(t *testing.T) {
		req := httptest.NewRequest("PUT", "/templates/"+templateId.String(), bytes.NewReader([]byte("invalid json")))
		req.SetPathValue("templateId", templateId.String())
		w := httptest.NewRecorder()

		handler.HandlePutTemplate(w, req)

		if w.Code != http.StatusBadRequest {
			t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
		}
		if !bytes.Contains(w.Body.Bytes(), []byte("invalid JSON payload")) {
			t.Errorf("Expected error message about invalid JSON, got: %s", w.Body.String())
		}
	})
}
