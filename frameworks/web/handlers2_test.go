package web_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func setup2(t *testing.T) (*web.CommandHandler, *web.DesignQueryHandler, *gateways.FakeRelDb) {
	t.Helper()
	logger := slog.Default()
	schema := controllers.Schema(jsonSchemaFilename)
	r := gateways.NewFakeRelDb()
	designFetcher := usecases.NewDesignRetriever(r)
	ai := gateways.NewFakeLLM()
	queryController := controllers.NewDesignRetrievalController(designFetcher)
	readHandler := web.NewDesignQueryHandler(logger, queryController)
	designCreator := usecases.NewDesignCreater(r, ai, logger)
	designUpdater := usecases.NewDesignUpdater(r)
	designSaver := usecases.NewDesignSaver(r)
	bulkDesignSaver := usecases.NewBulkDesignSaver(r, nil)
	designDeleter := usecases.NewDesignDeleter(r)
	cmdController := controllers.NewDesignWriteController(
		designCreator, designUpdater, designSaver, bulkDesignSaver, designDeleter, logger)
	writeHandler := web.NewCommandHandler(logger, schema, cmdController)
	return writeHandler, readHandler, r
}

func TestAddingNewDesign(t *testing.T) {
	writeHandler, readHandler, memStore := setup2(t)
	testDesign := genDesign()
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	req := httptest.NewRequest("POST", fmt.Sprintf("/projects/%s/designs", projId), bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePost(recorder, req)
	if status := recorder.Code; status != http.StatusCreated {
		t.Fatalf("handler returned wrong status code: got %v want %v", status, http.StatusCreated)
	}
	storedDesigns, err := memStore.DesignsForProject(t.Context(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(storedDesigns), 1; got != want {
		t.Fatalf("wrong number of stored designs: got %d want %d", got, want)
	}
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/%s/designs", projId), nil)
	req.SetPathValue("projectId", projId)
	recorder = httptest.NewRecorder()
	readHandler.HandleListDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	var designs []adapters.Design
	if err := json.Unmarshal(recorder.Body.Bytes(), &designs); err != nil {
		t.Fatal(err)
	}
	got, err := designs[0].ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	testDesign.ID = got.ID.String()
	want, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	want.LastUpdated = got.LastUpdated
	want.Title = got.Title
	want.Description = got.Description
	assert.Equal(t, want, got)
}

func TestChangingDesign(t *testing.T) {
	writeHandler, readHandler, memStore := setup2(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	ucDesign, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign); err != nil {
		t.Fatal(err)
	}
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	want, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePut(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetSpecifiedDesign(recorder, req)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	var design adapters.Design
	if err := json.Unmarshal(recorder.Body.Bytes(), &design); err != nil {
		t.Fatal(err)
	}
	got, err := design.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	want.LastUpdated = got.LastUpdated
	assert.Equal(t, want, got)
}

func TestRemovingDesign(t *testing.T) {
	writeHandler, readHandler, memStore := setup2(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	ucDesign, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign); err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("DELETE", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	writeHandler.HandleDelete(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetSpecifiedDesign(recorder, req)
	if status := recorder.Code; status != http.StatusNotFound {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNotFound)
	}
}

func TestRetrievingDesignsForMultipleProjects(t *testing.T) {
	const paramName = "ids"
	_, readHandler, memStore := setup2(t)
	projId2 := entities.NewProjectId("PRJ-FOOBAR2")
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	ucDesign, err := testDesign.ToUsecaseDesign(projId)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign); err != nil {
		t.Fatal(err)
	}
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	ucDesign2, err := testDesign2.ToUsecaseDesign(projId2)
	if err != nil {
		t.Fatal(err)
	}
	if _, err := memStore.UpsertDesign(t.Context(), ucDesign2); err != nil {
		t.Fatal(err)
	}
	params := url.Values{}
	params.Add(paramName, fmt.Sprintf("%s,%s", projId, projId2))
	req := httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder := httptest.NewRecorder()
	readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	if status := recorder.Code; status != http.StatusMultiStatus {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
	params.Set(paramName, fmt.Sprintf("%s,%s", projId, entities.NewProjectId("PRJ-PHANTOM")))
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder = httptest.NewRecorder()
	readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	if status := recorder.Code; status != http.StatusMultiStatus {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
}

func TestAddingMultipleDesignsForProject(t *testing.T) {
	writeHandler, _, memStore := setup2(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	data, err := json.Marshal([]adapters.Design{testDesign, testDesign2})
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs", projId)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	writeHandler.HandlePutAllDesignsForProject(recorder, req)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := memStore.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 2; got != want {
		t.Errorf("wrong number of results: got %d want %d", got, want)
	}
}
