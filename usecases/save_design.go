package usecases

import (
	"context"
)

type DesignSaver struct {
	designRepo designRepository
}

func NewDesignSaver(designRepo designRepository) *DesignSaver {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	return &DesignSaver{designRepo: designRepo}
}

func (dc *DesignSaver) SaveDesign(ctx context.Context, presenter OutcomePresenter, design Design) {
	var err error
	if design.ID, err = dc.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccess()
}
