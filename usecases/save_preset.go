package usecases

import (
	"context"
	"log/slog"
)

type PresetSaver struct {
	presetRepo presetRepository
	logger     *slog.Logger
}

func NewPresetSaver(presetRepo presetRepository, logger *slog.Logger) *PresetSaver {
	if IsNil(presetRepo) {
		panic("presetRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &PresetSaver{presetRepo: presetRepo, logger: logger}
}

func (dc *PresetSaver) SavePreset(ctx context.Context, presenter PresetCreationOutcomePresenter, preset Preset) {
	if preset.Id == "" {
		dc.logger.ErrorContext(ctx, "Attempt to save preset with no ID")
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	if err := dc.presetRepo.InsertPreset(ctx, preset); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccessWithNewResource(preset)
}
