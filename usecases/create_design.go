package usecases

import (
	"context"
	"database/sql"
	"log/slog"
)

type DesignCreater struct {
	designRepo designRepository
	genAI      GenAI
	logger     *slog.Logger
}

func NewDesignCreater(designRepo designRepository, genAI GenAI, logger *slog.Logger) *DesignCreater {
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if IsNil(genAI) {
		panic("genAI cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignCreater{designRepo: designRepo, genAI: genAI, logger: logger}
}

func (dc *DesignCreater) CreateDesign(ctx context.Context, presenter DesignMutationOutcomePresenter, design Design) {
	var err error
	// TODO: Generate title and description asynchronously to reduce latency.
	title, description, err := dc.genAI.GenerateDesignTitleAndDescription(ctx, design)
	if err != nil {
		dc.logger.ErrorContext(ctx, "Failed to generate title and description for new design",
			slog.String("designId", design.ID.String()), slog.String("error", err.Error()))
	} else {
		design.Title = sql.NullString{String: title, Valid: true}
		design.Description = sql.NullString{String: description, Valid: true}
	}
	if design.ID, err = dc.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccessWithResource(design, Created)
}
