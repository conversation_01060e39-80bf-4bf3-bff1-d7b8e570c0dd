package usecases_test

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Mock presenter for mutation outcome
type mockMutationOutcomePresenter struct {
	mock.Mock
}

func (m *mockMutationOutcomePresenter) PresentError(err error) {
	m.Called(err)
}

func (m *mockMutationOutcomePresenter) ConveySuccess() {
	m.Called()
}

func (m *mockMutationOutcomePresenter) ConveySuccessWithNewResource(design usecases.Design) {
	m.Called(design)
}

func TestNewDesignDeleter(t *testing.T) {
	t.Run("should create deleter with valid repository", func(t *testing.T) {
		repo := &mockDesignRepository{}
		deleter := usecases.NewDesignDeleter(repo)
		assert.NotNil(t, deleter)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignDeleter(nil)
		})
	})
}

func TestDesignDeleter_DeleteDesign(t *testing.T) {
	ctx := context.Background()

	t.Run("should delete design successfully with valid UUID", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		deleter := usecases.NewDesignDeleter(repo)

		validId := uuid.New()
		repo.On("DeleteDesign", ctx, validId).Return(nil)
		presenter.On("ConveySuccess").Return()

		deleter.DeleteDesign(ctx, presenter, validId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when design ID is zero UUID", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		deleter := usecases.NewDesignDeleter(repo)

		var zeroUUID uuid.UUID // This creates a zero UUID (00000000-0000-0000-0000-000000000000)
		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		deleter.DeleteDesign(ctx, presenter, zeroUUID)

		// Repository should not be called for zero UUID
		repo.AssertNotCalled(t, "DeleteDesign")
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when design ID is uuid.Nil", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		deleter := usecases.NewDesignDeleter(repo)

		// Based on the memory, uuid.Nil might not be the expected zero UUID
		// but the code checks for both zeroUUID and uuid.Nil
		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		deleter.DeleteDesign(ctx, presenter, uuid.Nil)

		// Repository should not be called for uuid.Nil
		repo.AssertNotCalled(t, "DeleteDesign")
		presenter.AssertExpectations(t)
	})

	t.Run("should handle repository error during deletion", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		deleter := usecases.NewDesignDeleter(repo)

		validId := uuid.New()
		dbError := errors.New("foreign key constraint violation")
		repo.On("DeleteDesign", ctx, validId).Return(dbError)
		presenter.On("ConveySuccess").Return() // Note: The current implementation doesn't handle repo errors

		deleter.DeleteDesign(ctx, presenter, validId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle different valid UUID formats", func(t *testing.T) {
		// Test with various valid UUIDs to ensure they all work
		validUUIDs := []uuid.UUID{
			uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			uuid.MustParse("ffffffff-ffff-ffff-ffff-ffffffffffff"),
			uuid.New(),
		}

		for i, validId := range validUUIDs {
			t.Run(fmt.Sprintf("valid_uuid_%d", i), func(t *testing.T) {
				repo := &mockDesignRepository{}
				presenter := &mockMutationOutcomePresenter{}
				deleter := usecases.NewDesignDeleter(repo)

				repo.On("DeleteDesign", ctx, validId).Return(nil)
				presenter.On("ConveySuccess").Return()

				deleter.DeleteDesign(ctx, presenter, validId)

				repo.AssertExpectations(t)
				presenter.AssertExpectations(t)
			})
		}
	})
}

// Test to verify the zero UUID detection logic specifically
func TestZeroUUIDDetection(t *testing.T) {
	var zeroUUID uuid.UUID

	t.Run("zero UUID should be detected correctly", func(t *testing.T) {
		// Verify that a zero-value UUID is indeed all zeros
		expected := "00000000-0000-0000-0000-000000000000"
		assert.Equal(t, expected, zeroUUID.String())
	})

	t.Run("uuid.Nil comparison", func(t *testing.T) {
		// Test the relationship between zero UUID and uuid.Nil
		// Based on the memory, these might not be equal in this codebase
		t.Logf("Zero UUID: %s", zeroUUID.String())
		t.Logf("uuid.Nil: %s", uuid.Nil.String())

		// The test should verify the actual behavior in the codebase
		// This helps document the UUID handling quirk mentioned in the memory
	})
}
