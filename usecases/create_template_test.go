package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type mockTemplateRepository struct {
	mock.Mock
}

func (m *mockTemplateRepository) InsertTemplate(ctx context.Context, template usecases.Template) (uuid.UUID, error) {
	args := m.Called(ctx, template)
	return args.Get(0).(uuid.UUID), args.Error(1)
}

func (m *mockTemplateRepository) ReadTemplate(ctx context.Context, templateId uuid.UUID) (usecases.Template, error) {
	args := m.Called(ctx, templateId)
	return args.Get(0).(usecases.Template), args.Error(1)
}

func (m *mockTemplateRepository) ReadAllTemplates(ctx context.Context) ([]usecases.Template, error) {
	args := m.Called(ctx)
	return args.Get(0).([]usecases.Template), args.Error(1)
}

type mockTemplateCreationOutcomePresenter struct {
	mock.Mock
}

func (m *mockTemplateCreationOutcomePresenter) PresentError(err error) {
	m.Called(err)
}

func (m *mockTemplateCreationOutcomePresenter) ConveySuccessWithNewResource(template usecases.Template) {
	m.Called(template)
}

func TestNewTemplateCreater(t *testing.T) {
	t.Run("should create creater with valid repository", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		creater := usecases.NewTemplateCreater(repo, nil)
		assert.NotNil(t, creater)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewTemplateCreater(nil, nil)
		})
	})
}

func TestTemplateCreater_CreateTemplate(t *testing.T) {
	ctx := context.Background()

	testTemplate := usecases.Template{
		ColorScheme:     usecases.Neutral,
		Style:           usecases.Modern,
		Name:            "Test Template",
		Description:     "A test template",
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm", "modern"},
		ColorPalette:    []string{"white", "gray"},
		MaterialPalette: []string{"marble", "wood"},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: &[]uuid.UUID{uuid.New()}[0],
			Lighting:  &[]uuid.UUID{uuid.New()}[0],
			Mirror:    &[]uuid.UUID{uuid.New()}[0],
			Paint:     &[]uuid.UUID{uuid.New()}[0],
			Shelving:  &[]uuid.UUID{uuid.New()}[0],
			Toilet:    &[]uuid.UUID{uuid.New()}[0],
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.VanityWall,
	}

	t.Run("should create template successfully", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		newTemplateId := uuid.New()
		repo.On("InsertTemplate", ctx, testTemplate).Return(newTemplateId, nil)
		createdTemplate := testTemplate
		createdTemplate.ID = newTemplateId
		presenter.On("ConveySuccessWithNewResource", createdTemplate).Return()

		creater.CreateTemplate(ctx, presenter, testTemplate)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		dbError := errors.New("database constraint violation")
		repo.On("InsertTemplate", ctx, testTemplate).Return(uuid.UUID{}, dbError)
		presenter.On("PresentError", dbError).Return()

		creater.CreateTemplate(ctx, presenter, testTemplate)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when template name is empty", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		emptyNameTemplate := testTemplate
		emptyNameTemplate.Name = ""

		presenter.On("PresentError", usecases.ErrInvalidPayload).Return()

		creater.CreateTemplate(ctx, presenter, emptyNameTemplate)

		// Repository should not be called when validation fails
		repo.AssertNotCalled(t, "InsertTemplate")
		presenter.AssertExpectations(t)
	})

	t.Run("should handle template with vanity scaling options", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		templateWithVanityOptions := testTemplate
		templateWithVanityOptions.VanityScalingOptions = map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		}

		newTemplateId := uuid.New()
		repo.On("InsertTemplate", ctx, templateWithVanityOptions).Return(newTemplateId, nil)
		createdTemplate := templateWithVanityOptions
		createdTemplate.ID = newTemplateId
		presenter.On("ConveySuccessWithNewResource", createdTemplate).Return()

		creater.CreateTemplate(ctx, presenter, templateWithVanityOptions)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle template with provenance data", func(t *testing.T) {
		repo := &mockTemplateRepository{}
		presenter := &mockTemplateCreationOutcomePresenter{}
		creater := usecases.NewTemplateCreater(repo, nil)

		templateWithProvenance := testTemplate
		templateWithProvenance.TemplateProvenance = usecases.TemplateProvenance{
			LightingBrand: &[]string{"Test Lighting Brand"}[0],
			PlumbingBrand: &[]string{"Test Plumbing Brand"}[0],
			ToiletBrand:   &[]string{"Test Toilet Brand"}[0],
			VanityBrand:   &[]string{"Test Vanity Brand"}[0],
			VanityStorage: &[]string{"Test Storage"}[0],
		}

		newTemplateId := uuid.New()
		repo.On("InsertTemplate", ctx, templateWithProvenance).Return(newTemplateId, nil)
		createdTemplate := templateWithProvenance
		createdTemplate.ID = newTemplateId
		presenter.On("ConveySuccessWithNewResource", createdTemplate).Return()

		creater.CreateTemplate(ctx, presenter, templateWithProvenance)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}
