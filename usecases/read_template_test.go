package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type mockTemplateRepositoryReplica struct {
	mock.Mock
}

func (m *mockTemplateRepositoryReplica) ReadTemplate(ctx context.Context, templateId uuid.UUID) (usecases.Template, error) {
	args := m.Called(ctx, templateId)
	return args.Get(0).(usecases.Template), args.Error(1)
}

func (m *mockTemplateRepositoryReplica) ReadAllTemplates(ctx context.Context) ([]usecases.Template, error) {
	args := m.Called(ctx)
	return args.Get(0).([]usecases.Template), args.Error(1)
}

type mockTemplatesPresenter struct {
	mock.Mock
}

func (m *mockTemplatesPresenter) PresentError(err error) {
	m.Called(err)
}

func (m *mockTemplatesPresenter) PresentTemplate(ctx context.Context, template usecases.Template) {
	m.Called(ctx, template)
}

func (m *mockTemplatesPresenter) PresentTemplates(ctx context.Context, templates []usecases.Template) {
	m.Called(ctx, templates)
}

func TestNewTemplateRetriever(t *testing.T) {
	t.Run("should create retriever with valid repository", func(t *testing.T) {
		repo := &mockTemplateRepositoryReplica{}
		retriever := usecases.NewTemplateRetriever(repo)
		assert.NotNil(t, retriever)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewTemplateRetriever(nil)
		})
	})
}

func TestTemplateRetriever_RetrieveTemplate(t *testing.T) {
	ctx := context.Background()
	templateId := uuid.New()
	testTemplate := usecases.Template{
		ID:          templateId,
		Name:        "Test Template",
		ColorScheme: usecases.Neutral,
		Style:       usecases.Modern,
	}

	t.Run("should retrieve template successfully", func(t *testing.T) {
		repo := &mockTemplateRepositoryReplica{}
		presenter := &mockTemplatesPresenter{}
		retriever := usecases.NewTemplateRetriever(repo)

		repo.On("ReadTemplate", ctx, templateId).Return(testTemplate, nil)
		presenter.On("PresentTemplate", ctx, testTemplate).Return()

		retriever.RetrieveTemplate(ctx, presenter, templateId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockTemplateRepositoryReplica{}
		presenter := &mockTemplatesPresenter{}
		retriever := usecases.NewTemplateRetriever(repo)

		dbError := errors.New("database error")
		repo.On("ReadTemplate", ctx, templateId).Return(usecases.Template{}, dbError)
		presenter.On("PresentError", dbError).Return()

		retriever.RetrieveTemplate(ctx, presenter, templateId)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}

func TestTemplateRetriever_RetrieveAllTemplates(t *testing.T) {
	ctx := context.Background()
	testTemplates := []usecases.Template{
		{
			ID:          uuid.New(),
			Name:        "Template 1",
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		{
			ID:          uuid.New(),
			Name:        "Template 2",
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
	}

	t.Run("should retrieve all templates successfully", func(t *testing.T) {
		repo := &mockTemplateRepositoryReplica{}
		presenter := &mockTemplatesPresenter{}
		retriever := usecases.NewTemplateRetriever(repo)

		repo.On("ReadAllTemplates", ctx).Return(testTemplates, nil)
		presenter.On("PresentTemplates", ctx, testTemplates).Return()

		retriever.RetrieveAllTemplates(ctx, presenter)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockTemplateRepositoryReplica{}
		presenter := &mockTemplatesPresenter{}
		retriever := usecases.NewTemplateRetriever(repo)

		dbError := errors.New("database error")
		repo.On("ReadAllTemplates", ctx).Return([]usecases.Template{}, dbError)
		presenter.On("PresentError", dbError).Return()

		retriever.RetrieveAllTemplates(ctx, presenter)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})
}
