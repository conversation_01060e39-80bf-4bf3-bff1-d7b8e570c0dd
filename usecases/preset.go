package usecases

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type Preset struct {
	Id         string    `json:"id"`
	TemplateId uuid.UUID `json:"templateId"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`

	RoomLayout   entities.RoomLayout `json:"roomLayout"`
	Measurements json.RawMessage     `json:"measurements"`
	Design       Design              `json:"design"`
	Rendition    entities.Rendition  `json:"rendition"`
}
