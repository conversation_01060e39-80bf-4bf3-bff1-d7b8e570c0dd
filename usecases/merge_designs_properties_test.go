package usecases_test

import (
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/leanovate/gopter"
	"github.com/leanovate/gopter/gen"
	"github.com/leanovate/gopter/prop"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

var (
	statuses     = []usecases.DesignStatus{usecases.Preview, usecases.Fave, usecases.Archived}
	num_statuses = len(statuses)
)

func GenUUID() gopter.Gen {
	return func(genParams *gopter.GenParameters) *gopter.GenResult {
		uuid := uuid.New()
		return gopter.NewGenResult(uuid, gopter.NoShrinker)
	}
}

// Property-based tests for MergeDesigns function
func TestMergeDesigns_PropertyBased(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property 1: ID should never change during merge
	properties.Property("ID is preserved during merge", prop.ForAll(
		func(id uuid.UUID, status uint16) bool {
			idx := int(status) % num_statuses
			designStatus := statuses[idx]
			original := createTestDesign(id, designStatus)
			diff := usecases.DesignDiff{ID: id}
			result := usecases.MergeDesigns(original, diff)
			return result.ID == id
		},
		GenUUID(),
		gen.UInt16(),
	))

	// Property 2: ProjectID should never change during merge
	properties.Property("ProjectID is preserved during merge", prop.ForAll(
		func(id uuid.UUID, status uint16) bool {
			idx := int(status) % num_statuses
			designStatus := statuses[idx]

			original := createTestDesign(id, designStatus)
			diff := usecases.DesignDiff{ID: id}
			result := usecases.MergeDesigns(original, diff)
			return result.ProjectID == original.ProjectID
		},
		GenUUID(),
		gen.UInt16(),
	))

	// Property 3: LastUpdated should always be updated
	properties.Property("LastUpdated is always updated", prop.ForAll(
		func(id uuid.UUID, status uint16) bool {
			idx := int(status) % num_statuses
			designStatus := statuses[idx]

			original := createTestDesign(id, designStatus)
			diff := usecases.DesignDiff{ID: id}
			result := usecases.MergeDesigns(original, diff)
			return result.LastUpdated.After(original.LastUpdated)
		},
		GenUUID(),
		gen.UInt16(),
	))

	// Property 4: Non-nil diff fields should override original fields
	properties.Property("non-nil diff status overrides original", prop.ForAll(
		func(id uuid.UUID, originalStatusInt uint16, newStatusInt uint16) bool {
			idx1 := int(originalStatusInt) % num_statuses
			idx2 := int(newStatusInt) % num_statuses
			originalStatus := statuses[idx1]
			newStatus := statuses[idx2]

			original := createTestDesign(id, originalStatus)
			diff := usecases.DesignDiff{
				ID:     id,
				Status: &newStatus,
			}
			result := usecases.MergeDesigns(original, diff)
			return result.Status == newStatus
		},
		GenUUID(),
		gen.UInt16(),
		gen.UInt16(),
	))

	// Property 5: Valid SQL null fields should override original fields
	properties.Property("valid SQL null fields override original", prop.ForAll(
		func(id uuid.UUID, newTitle string) bool {
			original := createTestDesign(id, usecases.Preview)

			diff := usecases.DesignDiff{ID: id}
			diff.Title = sql.NullString{String: newTitle, Valid: true}

			result := usecases.MergeDesigns(original, diff)
			return result.Title.String == newTitle && result.Title.Valid
		},
		GenUUID(),
		gen.AlphaString(),
	))

	// Property 6: Invalid SQL null fields should not override original fields
	properties.Property("invalid SQL null fields preserve original", prop.ForAll(
		func(id uuid.UUID) bool {
			original := createTestDesign(id, usecases.Preview)

			diff := usecases.DesignDiff{ID: id}
			diff.Title = sql.NullString{Valid: false}
			diff.ShowerGlassVisible = sql.NullBool{Valid: false}

			result := usecases.MergeDesigns(original, diff)
			return result.Title == original.Title &&
				result.ShowerGlassVisible == original.ShowerGlassVisible
		},
		GenUUID(),
	))

	// Property 7: Wallpaper placement changes should be applied
	properties.Property("wallpaper placement changes are applied", prop.ForAll(
		func(id uuid.UUID, placementInt uint16) bool {
			placements := []usecases.WallpaperPlacement{usecases.NoWallpaper, usecases.AllWalls, usecases.VanityWall}
			idx := int(placementInt) % len(placements)
			newPlacement := placements[idx]

			original := createTestDesign(id, usecases.Preview)
			diff := usecases.DesignDiff{
				ID:                 id,
				WallpaperPlacement: &newPlacement,
			}

			result := usecases.MergeDesigns(original, diff)
			return result.WallpaperPlacement == newPlacement
		},
		GenUUID(),
		gen.UInt16(),
	))

	// Property 8: Boolean visibility fields should be applied when valid
	properties.Property("boolean visibility fields are applied when valid", prop.ForAll(
		func(id uuid.UUID, showShowerGlass bool, showTubDoor bool) bool {
			original := createTestDesign(id, usecases.Preview)

			diff := usecases.DesignDiff{ID: id}
			diff.ShowerGlassVisible = sql.NullBool{Bool: showShowerGlass, Valid: true}
			diff.TubDoorVisible = sql.NullBool{Bool: showTubDoor, Valid: true}

			result := usecases.MergeDesigns(original, diff)
			return result.ShowerGlassVisible == showShowerGlass &&
				result.TubDoorVisible == showTubDoor
		},
		GenUUID(),
		gen.Bool(),
		gen.Bool(),
	))

	properties.TestingRun(t)
}

// Helper function to create a test design
func createTestDesign(id uuid.UUID, status usecases.DesignStatus) usecases.Design {
	return usecases.Design{
		ID:                 id,
		ProjectID:          "TEST-PROJECT",
		Created:            time.Now().Add(-time.Hour),
		LastUpdated:        time.Now().Add(-time.Hour),
		Status:             status,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			ColorScheme: &[]usecases.ColorScheme{usecases.Neutral}[0],
			Style:       &[]usecases.Style{usecases.Traditional}[0],
			Title:       sql.NullString{String: "Test Design", Valid: true},
		},
		ShowerGlassVisible: false,
		TubDoorVisible:     false,
		NichesVisible:      false,
	}
}

// Property-based test for edge cases
func TestMergeDesigns_EdgeCaseProperties(t *testing.T) {
	properties := gopter.NewProperties(nil)

	// Property: Merging with nil pointers should preserve original values
	properties.Property("nil pointers preserve original values", prop.ForAll(
		func(id uuid.UUID) bool {
			original := createTestDesign(id, usecases.Fave)

			// Create diff with nil pointers
			diff := usecases.DesignDiff{
				ID:                 id,
				Status:             nil,
				WallpaperPlacement: nil,
				WallTilePlacement:  nil,
			}
			// Don't set any DesignOptions fields (they should remain nil)

			result := usecases.MergeDesigns(original, diff)

			// Original values should be preserved
			return result.Status == original.Status &&
				result.WallpaperPlacement == original.WallpaperPlacement &&
				result.WallTilePlacement == original.WallTilePlacement &&
				result.ColorScheme == original.ColorScheme &&
				result.Style == original.Style
		},
		GenUUID(),
	))

	// Property: Multiple field changes should all be applied
	properties.Property("multiple field changes are all applied", prop.ForAll(
		func(id uuid.UUID, status uint16, placement uint16, tile uint16) bool {
			statuses := []usecases.DesignStatus{usecases.Preview, usecases.Fave, usecases.Archived}
			placements := []usecases.WallpaperPlacement{usecases.NoWallpaper, usecases.AllWalls, usecases.VanityWall}
			tilePlacements := []usecases.WallTilePlacement{usecases.NoWallTile, usecases.FullWall, usecases.HalfWall}

			newStatus := statuses[int(status)%len(statuses)]
			newPlacement := placements[int(placement)%len(placements)]
			newTilePlacement := tilePlacements[int(tile)%len(tilePlacements)]

			original := createTestDesign(id, usecases.Preview)
			diff := usecases.DesignDiff{
				ID:                 id,
				Status:             &newStatus,
				WallpaperPlacement: &newPlacement,
				WallTilePlacement:  &newTilePlacement,
			}

			result := usecases.MergeDesigns(original, diff)

			return result.Status == newStatus &&
				result.WallpaperPlacement == newPlacement &&
				result.WallTilePlacement == newTilePlacement
		},
		GenUUID(),
		gen.UInt16(),
		gen.UInt16(),
		gen.UInt16(),
	))

	properties.TestingRun(t)
}
