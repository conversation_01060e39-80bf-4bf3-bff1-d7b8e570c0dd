package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewDesignSaver(t *testing.T) {
	t.Run("should create saver with valid repository", func(t *testing.T) {
		repo := &mockDesignRepository{}
		saver := usecases.NewDesignSaver(repo)
		assert.NotNil(t, saver)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignSaver(nil)
		})
	})
}

func TestDesignSaver_SaveDesign(t *testing.T) {
	ctx := context.Background()
	projectId := entities.ProjectId("TEST-PROJECT")

	testDesign := usecases.Design{
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
	}

	t.Run("should save new design successfully", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		saver := usecases.NewDesignSaver(repo)

		newDesignId := uuid.New()
		designToSave := testDesign
		designToSave.ID = uuid.UUID{} // No ID initially

		repo.On("UpsertDesign", ctx, designToSave).Return(newDesignId, nil)
		presenter.On("ConveySuccess").Return()

		saver.SaveDesign(ctx, presenter, designToSave)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)

		// Note: The SaveDesign method modifies the design in place
		// The ID should be updated to the returned ID from the repository
	})

	t.Run("should save existing design successfully", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		saver := usecases.NewDesignSaver(repo)

		existingId := uuid.New()
		designToSave := testDesign
		designToSave.ID = existingId

		repo.On("UpsertDesign", ctx, designToSave).Return(existingId, nil)
		presenter.On("ConveySuccess").Return()

		saver.SaveDesign(ctx, presenter, designToSave)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)

		// Verify that the design ID remains the same
		assert.Equal(t, existingId, designToSave.ID)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		saver := usecases.NewDesignSaver(repo)

		dbError := errors.New("database constraint violation")
		repo.On("UpsertDesign", ctx, testDesign).Return(uuid.UUID{}, dbError)
		presenter.On("PresentError", dbError).Return()

		saver.SaveDesign(ctx, presenter, testDesign)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle design with all fields populated", func(t *testing.T) {
		repo := &mockDesignRepository{}
		presenter := &mockMutationOutcomePresenter{}
		saver := usecases.NewDesignSaver(repo)

		// Create a comprehensive design with all optional fields
		designId := uuid.New()
		faucetId := uuid.New()
		floorTileId := uuid.New()
		lightingId := uuid.New()
		mirrorId := uuid.New()
		paintId := uuid.New()
		shelvingId := uuid.New()
		showerFloorTileId := uuid.New()
		showerSystemId := uuid.New()
		showerWallTileId := uuid.New()
		showerShortWallTileId := uuid.New()
		showerGlassId := uuid.New()
		toiletId := uuid.New()
		tubId := uuid.New()
		tubDoorId := uuid.New()
		tubFillerId := uuid.New()
		vanityId := uuid.New()
		wallpaperId := uuid.New()
		wallTileId := uuid.New()
		nicheTileId := uuid.New()

		fullDesign := usecases.Design{
			ID:                 designId,
			ProjectID:          projectId,
			Status:             usecases.Fave,
			WallpaperPlacement: usecases.AllWalls,
			WallTilePlacement:  usecases.FullWall,
			DesignOptions: usecases.DesignOptions{
				ColorScheme:            &[]usecases.ColorScheme{usecases.Bold}[0],
				Style:                  &[]usecases.Style{usecases.Modern}[0],
				FloorTilePattern:       &[]usecases.TilePattern{usecases.Herringbone}[0],
				ShowerFloorTilePattern: &[]usecases.TilePattern{usecases.HorizontalStacked}[0],
				ShowerWallTilePattern:  &[]usecases.TilePattern{usecases.VerticalStacked}[0],
				WallTilePattern:        &[]usecases.TilePattern{usecases.HalfOffset}[0],
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile:       &floorTileId,
					Lighting:        &lightingId,
					Mirror:          &mirrorId,
					Paint:           &paintId,
					Shelving:        &shelvingId,
					ShowerFloorTile: &showerFloorTileId,
					ShowerWallTile:  &showerWallTileId,
					Toilet:          &toiletId,
					TubFiller:       &tubFillerId,
					Wallpaper:       &wallpaperId,
					WallTile:        &wallTileId,
				},
				Faucet:              &faucetId,
				NicheTile:           &nicheTileId,
				ShowerGlass:         &showerGlassId,
				ShowerShortWallTile: &showerShortWallTileId,
				ShowerSystem:        &showerSystemId,
				Tub:                 &tubId,
				TubDoor:             &tubDoorId,
				Vanity:              &vanityId,
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     true,
			NichesVisible:      true,
		}

		repo.On("UpsertDesign", ctx, fullDesign).Return(fullDesign.ID, nil)
		presenter.On("ConveySuccess").Return()

		saver.SaveDesign(ctx, presenter, fullDesign)

		repo.AssertExpectations(t)
		presenter.AssertExpectations(t)
	})

	t.Run("should handle different design statuses", func(t *testing.T) {
		statuses := []usecases.DesignStatus{
			usecases.Preview,
			usecases.Fave,
			usecases.Archived,
		}

		for _, status := range statuses {
			t.Run("status_"+string(status), func(t *testing.T) {
				repo := &mockDesignRepository{}
				presenter := &mockMutationOutcomePresenter{}
				saver := usecases.NewDesignSaver(repo)

				design := testDesign
				design.Status = status
				design.ID = uuid.New()

				repo.On("UpsertDesign", ctx, design).Return(design.ID, nil)
				presenter.On("ConveySuccess").Return()

				saver.SaveDesign(ctx, presenter, design)

				repo.AssertExpectations(t)
				presenter.AssertExpectations(t)
			})
		}
	})

	t.Run("should handle different wallpaper placements", func(t *testing.T) {
		placements := []usecases.WallpaperPlacement{
			usecases.NoWallpaper,
			usecases.AllWalls,
			usecases.VanityWall,
		}

		for _, placement := range placements {
			t.Run("wallpaper_"+string(placement), func(t *testing.T) {
				repo := &mockDesignRepository{}
				presenter := &mockMutationOutcomePresenter{}
				saver := usecases.NewDesignSaver(repo)

				design := testDesign
				design.WallpaperPlacement = placement
				design.ID = uuid.New()

				repo.On("UpsertDesign", ctx, design).Return(design.ID, nil)
				presenter.On("ConveySuccess").Return()

				saver.SaveDesign(ctx, presenter, design)

				repo.AssertExpectations(t)
				presenter.AssertExpectations(t)
			})
		}
	})

	t.Run("should handle different wall tile placements", func(t *testing.T) {
		placements := []usecases.WallTilePlacement{
			usecases.NoWallTile,
			usecases.FullWall,
			usecases.HalfWall,
			usecases.VanityFullWall,
			usecases.VanityHalfWall,
		}

		for _, placement := range placements {
			t.Run("wall_tile_"+string(placement), func(t *testing.T) {
				repo := &mockDesignRepository{}
				presenter := &mockMutationOutcomePresenter{}
				saver := usecases.NewDesignSaver(repo)

				design := testDesign
				design.WallTilePlacement = placement
				design.ID = uuid.New()

				repo.On("UpsertDesign", ctx, design).Return(design.ID, nil)
				presenter.On("ConveySuccess").Return()

				saver.SaveDesign(ctx, presenter, design)

				repo.AssertExpectations(t)
				presenter.AssertExpectations(t)
			})
		}
	})
}
